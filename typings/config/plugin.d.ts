// This file is created by egg-ts-helper
// Do not modify this file!!!!!!!!!
import 'egg';
import '@midwayjs/web';
import '@ali/egg';
import '@ali/midway';
import 'egg-onerror';
import 'egg-session';
import 'egg-i18n';
import 'egg-watcher';
import 'egg-multipart';
import 'egg-security';
import 'egg-development';
import 'egg-logrotator';
import 'egg-schedule';
import 'egg-static';
import 'egg-jsonp';
import 'egg-view';
import 'egg-userservice';
import 'egg-userrole';
import 'egg-validate';
import 'egg-tracer';
import '@ali/egg-session-store';
import '@ali/egg-security';
import 'egg-instrument';
import 'egg-cors';
import 'egg-rest';
import '@ali/egg-depapi';
import '@ali/egg-status';
import '@ali/egg-diamond';
import '@ali/egg-tair';
import '@ali/egg-hsfclient';
import '@ali/egg-configclient';
import '@ali/egg-vipclient';
import '@ali/egg-pandora';
import { EggPluginItem } from 'egg';
declare module 'egg' {
  interface EggPlugin {
    'onerror'?: EggPluginItem;
    'session'?: EggPluginItem;
    'i18n'?: EggPluginItem;
    'watcher'?: EggPluginItem;
    'multipart'?: EggPluginItem;
    'security'?: EggPluginItem;
    'development'?: EggPluginItem;
    'logrotator'?: EggPluginItem;
    'schedule'?: EggPluginItem;
    'static'?: EggPluginItem;
    'jsonp'?: EggPluginItem;
    'view'?: EggPluginItem;
    'userservice'?: EggPluginItem;
    'userrole'?: EggPluginItem;
    'validate'?: EggPluginItem;
    'tracer'?: EggPluginItem;
    'sessionstore'?: EggPluginItem;
    'aliSecurity'?: EggPluginItem;
    'instrument'?: EggPluginItem;
    'cors'?: EggPluginItem;
    'rest'?: EggPluginItem;
    'depapi'?: EggPluginItem;
    'status'?: EggPluginItem;
    'diamond'?: EggPluginItem;
    'tair'?: EggPluginItem;
    'hsfclient'?: EggPluginItem;
    'configclient'?: EggPluginItem;
    'vipclient'?: EggPluginItem;
    'pandora'?: EggPluginItem;
  }
}