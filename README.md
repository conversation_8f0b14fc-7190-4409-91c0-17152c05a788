# Switch Database 系统

Switch Database 是一个专门用于处理飞猪酒店对接层数据写入的 BaaS 系统。该系统主要解决了在 FaaS 应用中直连 OTS 进行数据写入时遇到的各种问题，提供了更稳定、可靠的数据写入服务。

## 解决问题
主要解决 FaaS 直写方案中存在的以下问题：
1. 写入量和速度难以控制，主要受上游调度频率和供应商返回数据量影响
2. OTS 写入资源消耗不透明
3. 异步写入可靠性难以保证
4. SDK 版本管理困难


## 功能特性

1. 中心化限流控制
   - 采用令牌桶算法
   - 支持 vendorCode + table 维度的每秒更新数量限制
   - 动态可调整的限流阈值

2. 异步队列处理
   - 基于 Redis + BullMQ 实现
   - 按供应商和更新任务维度设计队列
   - 保证各供应商任务互不阻塞

3. 数据一致性保证
   - 条件更新机制
   - 数据对比减少无效更新
   - 完整的任务执行状态追踪

## 使用指南

### 接口信息
- 预发环境：https://pre-switch-database.alibaba-inc.com
- 生产环境：https://switch-database.alibaba-inc.com
- API文档：https://xapi.alibaba-inc.com/nhn3hrle/uhs8vrfam06rxyc7

### 代码示例

```typescript
import axios from 'axios';
import { getEnv } from '@ali/env-util'

interface IBatchUpdateRowParams {
  traceId: string;
  uuid: string;
  rpcId: string;
  vendorType: string;
  vendorCode: string;
  updates: {
    tableName: string;
    data: any[];
    forceUpdate?: boolean;
  }[];
}

const env = getEnv();
const url = env === 'prod' 
  ? 'https://switch-database.alibaba-inc.com' 
  : 'https://pre-switch-database.alibaba-inc.com';

export const batchUpdateRow = async (params: IBatchUpdateRowParams) => {
  const res = await axios.post(`${url}/ots-writer/batch-write`, params);
  return res;
}
```

### 使用技巧
1. updates 数组按顺序执行，可用于处理表间依赖关系
2. 建议谨慎使用 forceUpdate，默认的条件更新机制效果良好

## 监控和运维

### 日志查看
- 工作台日志：https://fswitch.alibaba-inc.com/hotel/stability/log-search

### 任务监控
- 预发任务看板：https://pre-switch-database.alibaba-inc.com/bull-ui/
- 生产任务看板：https://switch-database.alibaba-inc.com/bull-ui/

### 性能监控
- Sunfire监控（预发）：https://sunfire.alibaba-inc.com/custom/59/product/preview/generalComp/7117
- Sunfire监控（生产）：https://sunfire.alibaba-inc.com/custom/59/product/preview/generalComp/7372

## 性能指标

基于预发环境单机压测结果：
- 单机TPS：100
- CPU峰值：30%
- 内存利用率：15%
- 任务处理能力：单worker并发10，单任务执行耗时0.2~0.3s

## 降级方案

```typescript
const extend = this.ctx.standardParams?.vendor?.extend || {};
const otsWriteDowngrade = extend?.otsWriteDowngrade || false;

if (otsWriteDowngrade) {
  // 原始 SDK 写入逻辑
} else {
  // Switch Database 写入逻辑
}
```

降级配置：通过线上工作台的配置/配置信息/扩展字段进行配置，支持实时生效。

## 贡献指南

如果您想为项目做出贡献，请：
1. Fork 本仓库
2. 创建您的特性分支
3. 提交您的更改
4. 确保提交信息清晰明确
5. 提交 Pull Request
