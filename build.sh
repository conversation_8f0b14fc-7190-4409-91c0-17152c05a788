#!/usr/bin/env bash
# for midway aone application
# midway-scripts 自动生成，请勿修改，并保证上传到 gitlab 上
# 版本号：9.3.2

set -e

BASE_DIR=${PWD}
# 禁止构建脚本在执行时覆写
export DISABLED_BUILD_OVERWRITE="true"
# 禁止老 node-scripts 脚本执行 postinstall
export NODE_SCRIPTS_SKIP_POSTINSTALL="true"

echo "------------- build.sh start, pwd: ${PWD}, schema: ${SCHEMA_NAME}, envType: ${ENV_TYPE}, appname: ${APP_NAME}, baseDir: ${BASE_DIR} at $(date +%Y/%m/%d\ %H:%M:%S) ------------"

safe_remove_dir() {
  # safe_remove 命令不存在就使用 rm -rf 代替
  # safe_remove 只在 aone 环境存在
  if command -v safe_remove >/dev/null 2>&1; then
    safe_remove $@
  else
    rm -rf $@
  fi
}

if [ -d ./node_modules ]; then
  echo "[midway-scripts] remove exist node_modules directory!"
  safe_remove_dir ./node_modules
fi

# 打印打包服务器的操作系统版本
cat /proc/version
ls -al /etc/*-release
cat /etc/*-release
uname -a

BEFORE_ALL=$(date +%s%3N)

# 如果当前构建环境已经有 node 并且版本大于 16，跳过 nvm 安装
# 如果没有 node，根据 engines 中的 install-node 安装 node 版本，默认使用 node 16
if [ -f package.json ] && [ $(node -pe 'parseInt(process.version.slice(1))' 2>/dev/null || echo 0) -lt 16 ]; then
  NODE_VERSION=$(node -p "require('./package.json').engines['install-node']" 2>/dev/null || echo 16)

  if [ "$NODE_VERSION" = "14" ]; then
    nvm install node-v14.20.1
  elif [ "$NODE_VERSION" = "16" ]; then
    nvm install node-v16.20.2
  else
    echo "Unsupported Node.js version: $NODE_VERSION and use node 16 instead"
    nvm install node-v16.20.2
  fi
fi

# 打印下 node 版本
echo "node version: $(node -v)"

# 禁用 tnpm 自动升级
export TNPM_UPDATER=false
TNPM_VERSION='latest-9'

# 设置 xprofiler 源地址，防止 npm 模式下安装到 github 包导致报错
# 如果是包含 package-lock.json 的锁包情况，使用 export 环境变量，否则赋值当前变量
if [ -f package-lock.json ]; then
  export npm_config_xprofiler_binary_host_mirror="https://anpm.alibaba-inc.com/mirrors/xprofiler"
else
  npm_config_xprofiler_binary_host_mirror="https://anpm.alibaba-inc.com/mirrors/xprofiler"
fi

NPM_TMP_GLOBAL_DIR=$(npm prefix -g)

# https://yuque.antfin.com/frontbase/tnpm/install-at-ci
export PATH=${BASE_DIR}/node_modules/.bin:${BASE_DIR}/.node/bin:${BASE_DIR}/.bin:${NPM_TMP_GLOBAL_DIR}/bin:${NPM_TMP_GLOBAL_DIR}/lib/node_modules/tnpm/bin:/usr/local/gcc-5.2.0/bin:$PATH
curl -v -L --retry 5 --retry-delay 3 -o- https://anpm.alibaba-inc.com/open/install-tnpm-standalone.sh | bash -s ${TNPM_VERSION}

tnpm -v

tnpm install || exit $?

customBuild=$(node -e "try{console.log(require('./package.json').scripts.customBuild || '')}catch(e){}")

# 生成 aone 部署所需要的文件
node ./node_modules/@ali/midway-scripts/generator.cjs || return 1
# 执行构建
tnpm run build || return 1
# alias 路径替换
tnpx tsc-alias -p tsconfig.json
# 构建检查
tnpx @ali/midway-build-check || return 1

# 如果 customBuild 有值，则执行 npm run customBuild
if [ -n "$customBuild" ]; then
  echo "run custom build command"
  npm run customBuild || return 1
fi

# 上报数据
node ./node_modules/@ali/midway-scripts/report.cjs

# 删除安装的 devDeps 依赖
if [ $(node -pe "require('./package.json').tnpm?.mode" 2> /dev/null) = "npm" ]; then
  tnpm prune --production
else
  # 使用 npminstall 模式的时候需要清空再重新安装线上依赖
  safe_remove_dir node_modules
  tnpm i --production || exit $?
fi
# http://gitlab.alibaba-inc.com/node/sigma/issues/26488 解决 CPU Share 化后 egg 自动获取到的核心数太多的问题
tnpm i @ali/sigma --production --no-save --no-package-lock

echo "build success"
