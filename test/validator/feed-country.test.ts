
import feedCountrySchema from '../../src/validator/schemas/feed-country.schema';

describe('feed-country.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 'huanya',
      countryName: '中国',
      countryNameTranslated: 'China',
      countryIso: 'CN',
      language: 'ZH_CN'
    };

    const result = feedCountrySchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      // 缺少 requestPk
      vendorCode: 'huanya',
      // 缺少 countryId
      vendorType: 'huanya',
      countryName: '中国',
      language: 'ZH_CN'
    };

    const result = feedCountrySchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryId'))).toBe(true);
    }
  });

  // 字段类型错误测试
  it('应该在字段类型错误时验证失败', () => {
    const invalidTypeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 123, // 应该是字符串
      countryName: 456, // 应该是字符串
      language: 'ZH_CN'
    };

    const result = feedCountrySchema.safeParse(invalidTypeData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorType'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryName'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidEnumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 'huanya',
      language: 'INVALID_LANGUAGE' // 无效的语言
    };

    const result = feedCountrySchema.safeParse(invalidEnumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('language'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const emptyStringData = {
      requestPk: '',
      vendorCode: '',
      countryId: '',
      vendorType: 'huanya',
      language: 'ZH_CN'
    };

    const result = feedCountrySchema.safeParse(emptyStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryId'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const dataWithOptionalFieldsMissing = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 'huanya',
      language: 'ZH_CN'
      // 缺少可选字段 countryName, countryNameTranslated 和 countryIso
    };

    const result = feedCountrySchema.safeParse(dataWithOptionalFieldsMissing);
    expect(result.success).toBe(true);
  });

  // 多条数据批量验证测试
  it('应该能够批量验证多条数据', () => {
    const validDataArray = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        vendorType: 'huanya',
        countryName: '中国',
        countryNameTranslated: 'China',
        countryIso: 'CN',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'US',
        vendorType: 'huanya',
        countryName: 'United States',
        countryNameTranslated: '美国',
        countryIso: 'US',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=JA_JP\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'JP',
        vendorType: 'huanya',
        countryName: '日本',
        countryNameTranslated: 'Japan',
        countryIso: 'JP',
        language: 'JA_JP'
      }
    ];

    // 验证每条数据
    const results = validDataArray.map(data => feedCountrySchema.safeParse(data));

    // 所有数据应该都通过验证
    expect(results.every(result => result.success)).toBe(true);
  });

  // 空对象测试
  it('应该在传入空对象时验证失败', () => {
    const emptyData = {};

    const result = feedCountrySchema.safeParse(emptyData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.length).toBeGreaterThan(0);
    }
  });

  // 额外字段测试
  it('应该允许额外的字段存在', () => {
    const dataWithExtraFields = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 'huanya',
      countryName: '中国',
      countryNameTranslated: 'China',
      countryIso: 'CN',
      language: 'ZH_CN',
      extraField1: 'extra value 1',
      extraField2: 'extra value 2'
    };

    const result = feedCountrySchema.safeParse(dataWithExtraFields);
    expect(result.success).toBe(true);
  });

  // 测试 countryName, countryNameTranslated 和 countryIso 为空字符串时的验证
  it('应该在可选字段为空字符串时验证失败', () => {
    const emptyOptionalStringData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      vendorType: 'huanya',
      countryName: '', // 空字符串
      countryNameTranslated: '', // 空字符串
      countryIso: '', // 空字符串
      language: 'ZH_CN'
    };

    const result = feedCountrySchema.safeParse(emptyOptionalStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('countryName'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryNameTranslated'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryIso'))).toBe(true);
    }
  });

  // 测试不同语言的支持
  it('应该支持不同的语言', () => {
    const validLanguages = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        vendorType: 'huanya',
        countryName: '中国',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        vendorType: 'huanya',
        countryName: 'China',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=JA_JP\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        vendorType: 'huanya',
        countryName: '中国',
        language: 'JA_JP'
      }
    ];

    const results = validLanguages.map(data => feedCountrySchema.safeParse(data));
    expect(results.every(result => result.success)).toBe(true);
  });
});

