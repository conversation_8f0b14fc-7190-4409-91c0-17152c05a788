import ariRateSchema from '../../src/validator/schemas/ari-rate';

describe('ari-rate.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "vendorType": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "ratePlanName": "小城故事大床房(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 1,
      "currency": "CNY",
      "roomInventory": false
    };

    const result = ariRateSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      // 缺少 hotelId
      roomTypeId: '765738',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      status: true,
      los: 2,
      occupancy: 1,
      currency: 'CNY',
      roomInventory: false
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('hotelId'))).toBe(true);
    }
  });

  // 字段类型错误测试
  it('应该在字段类型错误时验证失败', () => {
    const invalidData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      roomTypeId: '765738',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      status: 'true', // 应该是布尔值
      los: 2,
      occupancy: 1,
      currency: 'CNY',
      roomInventory: false
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('status'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      roomTypeId: '765738',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      status: true,
      los: 2,
      occupancy: 1,
      currency: 'INVALID_CURRENCY', // 无效的货币代码
      roomInventory: false
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('currency'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "",
      "vendorType": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "ratePlanName": "小城故事大床房(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 1,
      "currency": "CNY",
      "roomInventory": false
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      roomTypeId: '765738',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      status: true,
      los: 2,
      occupancy: 1,
      // currency: 'CNY',
      // roomInventory: false
    };

    const result = ariRateSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 整数测试
  it('应该验证los和occupancy为整数', () => {
    const invalidData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      roomTypeId: '765738',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      status: true,
      los: '2', // 应该是整数
      occupancy: '1' // 应该是整数
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('los'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('occupancy'))).toBe(true);
    }
  });

  // 多条数据批量测试
  it('应该能够批量验证多条数据', () => {
    const validData = [{
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "vendorType": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "ratePlanName": "小城故事大床房(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 1,
      "currency": "CNY",
      "roomInventory": false
    },
    {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "vendorType": "huanya",
      "rateKey": "5699988_48618208_2_2",
      "rateId": "48618208_2_2",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "ratePlanName": "小城故事大床房(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 2,
      "currency": "CNY",
      "roomInventory": false
    },
    {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "vendorType": "huanya",
      "rateKey": "5699988_48618205_2_1",
      "rateId": "48618205_2_1",
      "hotelId": "5699988",
      "roomTypeId": "12279480",
      "ratePlanId": "hy_5699988_12279480_48618205",
      "ratePlanName": "特价影音大床房（新风无窗）(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 1,
      "currency": "CNY",
      "roomInventory": false
    }];
    const result = validData.map(data => ariRateSchema.safeParse(data));
    expect(result.every(r => r.success)).toBe(true);
  });

  // rateKey 格式测试
  it('rateKey格式不正确时应该不通过', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "vendorType": "huanya",
      "rateKey": "5699988_48618208_2_1_invalid", // 格式不正确
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "ratePlanName": "小城故事大床房(标准价)",
      "status": true,
      "los": 2,
      "occupancy": 1,
      "currency": "CNY",
      "roomInventory": false
    };

    const result = ariRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('rateKey'))).toBe(true);
    }
  });

  // 边界值测试
  it('los和occupancy应该支持为0', () => {
    const edgeCaseData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      los: 0, // 边界值
      occupancy: 0 // 边界值
    };

    // 这个测试主要是检查 schema 是否对边界值有特殊处理
    // 如果没有特殊限制，这个数据应该是有效的
    const result = ariRateSchema.safeParse(edgeCaseData);
    expect(result.success).toBe(true);
  })

  // 类型转换测试
  it('status为字符串true时应该不通过', () => {
    const convertibleData = {
      requestPk: 'vendorCode=huanya\u0001env=prod',
      vendorCode: 'huanya',
      vendorType: 'huanya',
      rateKey: '5699988_48618208_2_1',
      rateId: '48618208_2_1',
      hotelId: '5699988',
      los: 2,
      occupancy: 1,
      currency: 'CNY',
      roomInventory: false,
      status: 'true' // 字符串 'true'
    };

    const result = ariRateSchema.safeParse(convertibleData);
    expect(result.success).toBe(false);
  });
});



// 线上真实数据参考
export const validData = [{
  "requestPk": "vendorCode=huanya\u0001env=prod",
  "vendorCode": "huanya",
  "vendorType": "huanya",
  "rateKey": "5699988_48618208_2_1",
  "rateId": "48618208_2_1",
  "hotelId": "5699988",
  "roomTypeId": "765738",
  "ratePlanId": "hy_5699988_765738_48618208",
  "ratePlanName": "小城故事大床房(标准价)",
  "status": true,
  "los": 2,
  "occupancy": 1,
  "currency": "CNY",
  "roomInventory": false
},
{
  "requestPk": "vendorCode=huanya\u0001env=prod",
  "vendorCode": "huanya",
  "vendorType": "huanya",
  "rateKey": "5699988_48618208_2_2",
  "rateId": "48618208_2_2",
  "hotelId": "5699988",
  "roomTypeId": "765738",
  "ratePlanId": "hy_5699988_765738_48618208",
  "ratePlanName": "小城故事大床房(标准价)",
  "status": true,
  "los": 2,
  "occupancy": 2,
  "currency": "CNY",
  "roomInventory": false
},
{
  "requestPk": "vendorCode=huanya\u0001env=prod",
  "vendorCode": "huanya",
  "vendorType": "huanya",
  "rateKey": "5699988_48618205_2_1",
  "rateId": "48618205_2_1",
  "hotelId": "5699988",
  "roomTypeId": "12279480",
  "ratePlanId": "hy_5699988_12279480_48618205",
  "ratePlanName": "特价影音大床房（新风无窗）(标准价)",
  "status": true,
  "los": 2,
  "occupancy": 1,
  "currency": "CNY",
  "roomInventory": false
}]