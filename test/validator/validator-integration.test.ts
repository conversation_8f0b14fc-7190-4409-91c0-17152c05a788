import { createApp, close } from '@midwayjs/mock';
import { Application } from 'egg';

import { OtsValidatorIntegration } from '../../src/validator/validator-integration';
import { ValidatorConfigManager } from '../../src/validator/config-manager';
import { ZodValidator } from '../../src/validator/zod-validator';
import { LogService } from '../../src/helper/log';


// 测试数据
const validFeedRatePlanData = [
  {
    requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
    vendorCode: 'huanya',
    vendorType: 'huanya',
    language: 'ZH_CN',
    hotelId: '5699988',
    ratePlanId: 'hy_5699988_765738_48618208',
    ratePlanName: '小城故事大床房(标准价)',
    channel: 'O',
    status: true,
    paymentType: 'PREPAY',
    priceType: 'COST',
    bookingRule: {
      minLos: 1,
      maxLos: 90,
      minRoomQuantity: 1,
      maxRoomQuantity: 5,
      minAdvanceHours: 0,
      maxAdvanceHours: 2161
    },
    meal: [
      {
        calendaring: false,
        number: 0,
        type: 'ROOM_ONLY'
      }
    ],
    cancelPolicies: [
      {
        cancelPolicyType: 'LIMITED_TIME',
        details: [
          {
            advanceHours: 4,
            rate: 0
          }
        ]
      }
    ]
  }
];

const invalidFeedRatePlanData = [
  {
    requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
    vendorCode: 'huanya',
    vendorType: 'huanya',
    language: 'ZH_CN',
    hotelId: '5699988',
    // 缺少必填字段 ratePlanId
    ratePlanName: '小城故事大床房(标准价)',
    channel: 'O',
    status: true,
    paymentType: 'INVALID_TYPE', // 无效的支付类型
    priceType: 'COST'
  }
];

describe('OTS 与验证器集成', () => { 
  let app: Application;
  let otsValidatorIntegration: OtsValidatorIntegration;
  let validatorConfigManager: ValidatorConfigManager;
  let zodValidator: ZodValidator;
  let logService: LogService;

  beforeAll(async () => {
    // 创建应用
    app = await createApp();
    // 获取测试所需要的服务实例
    otsValidatorIntegration = await app.getApplicationContext().getAsync(OtsValidatorIntegration);
    validatorConfigManager = await app.getApplicationContext().getAsync(ValidatorConfigManager);
    zodValidator = await app.getApplicationContext().getAsync(ZodValidator);
    logService = await app.getApplicationContext().getAsync(LogService);

    // 模拟 logService 方法
    logService.error = jest.fn();
    logService.sendRowHistoryLog = jest.fn();
  });

  afterAll(async () => {
    // 关闭应用
    await close(app);
  });
  
  beforeEach(() => {
    // 每次测试前，重置 mock
    jest.resetAllMocks();
  });

  describe('validateOtsData 方法', () => {
    it('当表未启用验证时，应该直接返回原始数据', async () => {
      // 模拟表未启用验证
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: false,
        failStrategy: 'log'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', validFeedRatePlanData);
      expect(result.action).toBe('continue');
      expect(result.data).toEqual(validFeedRatePlanData);

      // 验证是否调用了配置管理器，应该调用
      expect(validatorConfigManager.getTableConfig).toHaveBeenCalledWith('feed_rate_plan');
    });


    it('当表启用验证但无对应 schema 时，应该直接返回原始数据', async () => {
      // 模拟表启用验证但无对应 schema
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'log'
      });

      const result = await otsValidatorIntegration.validateOtsData('not_exist_table', validFeedRatePlanData);
      expect(result.action).toBe('continue');
      expect(result.data).toEqual(validFeedRatePlanData);

      // 验证是否调用了配置管理器，应该调用
      expect(validatorConfigManager.getTableConfig).toHaveBeenCalledWith('not_exist_table');
    });

    it('当数据验证通过时应该返回原始数据', async () => {
      // 模拟表启用验证且有对应 schema
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'log'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', validFeedRatePlanData);

      expect(result.action).toBe('continue');
      expect(result.data).toEqual(validFeedRatePlanData);

      // 验证是否调用了配置管理器，应该调用
      expect(validatorConfigManager.getTableConfig).toHaveBeenCalledWith('feed_rate_plan');

      // 验证是否记录了错误日志，不应该调用
      expect(logService.error).not.toHaveBeenCalled();
    });

    it('当数据验证失败且策略为abort时应中止操作', async () => { 
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'abort'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', invalidFeedRatePlanData);
      // 验证结果应该是 abort
      expect(result.action).toBe('abort');
      // 验证数据应该是 undefined
      expect(result.data).toBeUndefined();

      // 验证是否记录了错误日志，应该调用
      expect(logService.error).toHaveBeenCalled();
    })

    it('当数据验证失败且策略为skip时，应只返回有效数据', async () => { 
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'skip'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', [...validFeedRatePlanData,...invalidFeedRatePlanData]);

      // 验证结果应该是 continue
      expect(result.action).toBe('continue');
      // 验证数据应该只包含有效数据
      expect(result.data).toEqual(validFeedRatePlanData);
      // 验证是否记录了错误日志，应该调用
      expect(logService.error).toHaveBeenCalled();
    })

    it('当数据验证失败且策略为skip，但没有有效数据时，应中止操作', async () => { 
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'skip'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', invalidFeedRatePlanData);

      // 验证结果应该是 abort
      expect(result.action).toBe('abort');
      // 验证数据应该是 undefined
      expect(result.data).toBeUndefined();
      // 验证是否记录了错误日志，应该调用
      expect(logService.error).toHaveBeenCalled();
    })

    it('当数据验证失败且策略为log时，应返回原始数据', async () => { 
      jest.spyOn(validatorConfigManager, 'getTableConfig').mockResolvedValue({
        enable: true,
        failStrategy: 'log'
      });

      const result = await otsValidatorIntegration.validateOtsData('feed_rate_plan', invalidFeedRatePlanData);

      // 验证结果应该是 continue
      expect(result.action).toBe('continue');
      // 验证数据应该是原始数据
      expect(result.data).toEqual(invalidFeedRatePlanData);
      // 验证是否记录了错误日志，应该调用
      expect(logService.error).toHaveBeenCalled();
    })
  });
})
