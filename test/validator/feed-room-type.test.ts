import feedRoomTypeSchema from '../../src/validator/schemas/feed-room-type.schema';

describe('feed-room-type.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      maxOccupancy: 2,
      name: '豪华大床房',
      nameTranslated: 'Deluxe King Room',
      sizeOfRoom: '30',
      bed: [
        {
          bedType: 'KING',
          bedNum: 1,
          relation: 'OR'
        }
      ],
      bedTypeDesc: '1张大床'
    };

    const result = feedRoomTypeSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      // 缺少 requestPk
      vendorCode: 'huanya',
      // 缺少 hotelId
      roomTypeId: '765738',
      vendorType: 'huanya',
      name: '豪华大床房'
    };

    const result = feedRoomTypeSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelId'))).toBe(true);
    }
  });

  // 字段类型错误测试
  it('应该在字段类型错误时验证失败', () => {
    const invalidTypeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 123, // 应该是字符串
      maxOccupancy: '2', // 应该是整数
      name: 456 // 应该是字符串
    };

    const result = feedRoomTypeSchema.safeParse(invalidTypeData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorType'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('maxOccupancy'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('name'))).toBe(true);
    }
  });

  // 嵌套对象验证测试
  it('应该验证嵌套对象的有效性', () => {
    const invalidNestedData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      bed: [
        {
          // 缺少必填字段 bedType
          bedNum: 1,
          relation: 'INVALID_RELATION' // 无效的关系类型
        }
      ]
    };

    const result = feedRoomTypeSchema.safeParse(invalidNestedData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('bed'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidEnumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      bed: [
        {
          bedType: 'KING',
          bedNum: 1,
          relation: 'INVALID_RELATION' // 无效的关系类型
        }
      ]
    };

    const result = feedRoomTypeSchema.safeParse(invalidEnumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('bed'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const emptyStringData = {
      requestPk: '',
      vendorCode: '',
      hotelId: '',
      roomTypeId: '',
      vendorType: ''
    };

    const result = feedRoomTypeSchema.safeParse(emptyStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelId'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('roomTypeId'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('vendorType'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const dataWithOptionalFieldsMissing = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya'
      // 缺少可选字段 maxOccupancy, name, nameTranslated, sizeOfRoom, bed, bedTypeDesc
    };

    const result = feedRoomTypeSchema.safeParse(dataWithOptionalFieldsMissing);
    expect(result.success).toBe(true);
  });

  // 多条数据批量验证测试
  it('应该能够批量验证多条数据', () => {
    const validDataArray = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        roomTypeId: '765738',
        vendorType: 'huanya',
        name: '豪华大床房'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        roomTypeId: '765739',
        vendorType: 'huanya',
        name: '豪华双床房',
        bed: [
          {
            bedType: 'TWIN',
            bedNum: 2,
            relation: 'AND'
          }
        ]
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        roomTypeId: '765740',
        vendorType: 'huanya',
        maxOccupancy: 3,
        name: '家庭房',
        bed: [
          {
            bedType: 'KING',
            bedNum: 1,
            relation: 'AND'
          },
          {
            bedType: 'SINGLE',
            bedNum: 1,
            relation: 'AND'
          }
        ]
      }
    ];

    // 验证每条数据
    const results = validDataArray.map(data => feedRoomTypeSchema.safeParse(data));

    // 所有数据应该都通过验证
    expect(results.every(result => result.success)).toBe(true);
  });

  // 空对象测试
  it('应该在传入空对象时验证失败', () => {
    const emptyData = {};

    const result = feedRoomTypeSchema.safeParse(emptyData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.length).toBeGreaterThan(0);
    }
  });

  // 额外字段测试
  it('应该允许额外的字段存在', () => {
    const dataWithExtraFields = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      name: '豪华大床房',
      extraField1: 'extra value 1',
      extraField2: 'extra value 2'
    };

    const result = feedRoomTypeSchema.safeParse(dataWithExtraFields);
    expect(result.success).toBe(true);
  });

  // 测试可选字段为空字符串时的验证
  it('应该在可选字段为空字符串时验证失败', () => {
    const emptyOptionalStringData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      name: '', // 空字符串
      nameTranslated: '', // 空字符串
      sizeOfRoom: '', // 空字符串
      bedTypeDesc: '' // 空字符串
    };

    const result = feedRoomTypeSchema.safeParse(emptyOptionalStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('name'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('nameTranslated'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('sizeOfRoom'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('bedTypeDesc'))).toBe(true);
    }
  });

  // 测试 bedNum 为负数时的验证
  it('应该在 bedNum 为负数时验证失败', () => {
    const negativeBedNumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      bed: [
        {
          bedType: 'KING',
          bedNum: -1, // 负数
          relation: 'OR'
        }
      ]
    };

    const result = feedRoomTypeSchema.safeParse(negativeBedNumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('bed'))).toBe(true);
    }
  });

  // 测试 maxOccupancy 为负数时的验证
  it('应该允许 maxOccupancy 为负数', () => {
    const negativeMaxOccupancyData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      maxOccupancy: -2 // 负数
    };

    // 根据 BaseTypes.optional(BaseTypes.integer) 的定义，负数应该是有效的
    const result = feedRoomTypeSchema.safeParse(negativeMaxOccupancyData);
    expect(result.success).toBe(true);
  });

  // 测试 bed 数组为空时的验证
  it('应该允许 bed 数组为空', () => {
    const emptyBedArrayData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      bed: [] // 空数组
    };

    const result = feedRoomTypeSchema.safeParse(emptyBedArrayData);
    expect(result.success).toBe(true);
  });

  // 测试自定义验证规则
  it('应该通过自定义验证规则', () => {
    // 导入带有自定义验证规则的 schema
    const feedRoomTypeSchemaWithRefinements = require('@/validator/schemas/feed-room-type.schema').default;

    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      maxOccupancy: 2,
      name: '豪华大床房',
      bed: [
        {
          bedType: 'KING',
          bedNum: 1,
          relation: 'OR'
        }
      ]
    };

    const result = feedRoomTypeSchemaWithRefinements.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 测试多种床型组合
  it('应该支持多种床型组合', () => {
    const multipleBedTypesData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      roomTypeId: '765738',
      vendorType: 'huanya',
      bed: [
        {
          bedType: 'KING',
          bedNum: 1,
          relation: 'OR'
        },
        {
          bedType: 'TWIN',
          bedNum: 2,
          relation: 'AND'
        },
        {
          bedType: 'SINGLE',
          bedNum: 1,
          relation: 'AND'
        }
      ]
    };

    const result = feedRoomTypeSchema.safeParse(multipleBedTypesData);
    expect(result.success).toBe(true);
  });
});

// 线上真实数据参考
export const validData = [
  {
    requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
    vendorCode: 'huanya',
    hotelId: '5699988',
    roomTypeId: '765738',
    vendorType: 'huanya',
    maxOccupancy: 2,
    name: '豪华大床房',
    nameTranslated: 'Deluxe King Room',
    sizeOfRoom: '30',
    bed: [
      {
        bedType: 'KING',
        bedNum: 1,
        relation: 'OR'
      }
    ],
    bedTypeDesc: '1张大床'
  },
  {
    requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
    vendorCode: 'huanya',
    hotelId: '5699988',
    roomTypeId: '765739',
    vendorType: 'huanya',
    maxOccupancy: 2,
    name: '豪华双床房',
    nameTranslated: 'Deluxe Twin Room',
    sizeOfRoom: '30',
    bed: [
      {
        bedType: 'TWIN',
        bedNum: 2,
        relation: 'AND'
      }
    ],
    bedTypeDesc: '2张单人床'
  },
  {
    requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
    vendorCode: 'huanya',
    hotelId: '5699988',
    roomTypeId: '765740',
    vendorType: 'huanya',
    maxOccupancy: 3,
    name: '家庭房',
    nameTranslated: 'Family Room',
    sizeOfRoom: '40',
    bed: [
      {
        bedType: 'KING',
        bedNum: 1,
        relation: 'AND'
      },
      {
        bedType: 'SINGLE',
        bedNum: 1,
        relation: 'AND'
      }
    ],
    bedTypeDesc: '1张大床和1张单人床'
  }
];