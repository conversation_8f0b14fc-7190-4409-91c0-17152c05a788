import feedCitySchema from '../../src/validator/schemas/feed-city.schema';

describe('feed-city.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 'huanya',
      cityName: '上海',
      cityNameTranslated: 'Shanghai',
      language: 'ZH_CN'
    };

    const result = feedCitySchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      // 缺少 requestPk
      vendorCode: 'huanya',
      // 缺少 countryId
      cityId: 'SHA',
      vendorType: 'huanya',
      cityName: '上海',
      language: 'ZH_CN'
    };

    const result = feedCitySchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryId'))).toBe(true);
    }
  });

  // 字段类型错误测试
  it('应该在字段类型错误时验证失败', () => {
    const invalidTypeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 123, // 应该是字符串
      cityName: 456, // 应该是字符串
      language: 'ZH_CN'
    };

    const result = feedCitySchema.safeParse(invalidTypeData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorType'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('cityName'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidEnumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 'huanya',
      language: 'INVALID_LANGUAGE' // 无效的语言
    };

    const result = feedCitySchema.safeParse(invalidEnumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('language'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const emptyStringData = {
      requestPk: '',
      vendorCode: '',
      countryId: '',
      cityId: '',
      vendorType: 'huanya',
      language: 'ZH_CN'
    };

    const result = feedCitySchema.safeParse(emptyStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('countryId'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('cityId'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const dataWithOptionalFieldsMissing = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 'huanya',
      language: 'ZH_CN'
      // 缺少可选字段 cityName 和 cityNameTranslated
    };

    const result = feedCitySchema.safeParse(dataWithOptionalFieldsMissing);
    expect(result.success).toBe(true);
  });

  // 多条数据批量验证测试
  it('应该能够批量验证多条数据', () => {
    const validDataArray = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        cityId: 'SHA',
        vendorType: 'huanya',
        cityName: '上海',
        cityNameTranslated: 'Shanghai',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'US',
        cityId: 'NYC',
        vendorType: 'huanya',
        cityName: 'New York',
        cityNameTranslated: '纽约',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        cityId: 'BJS',
        vendorType: 'huanya',
        cityName: '北京',
        cityNameTranslated: 'Beijing',
        language: 'ZH_CN'
      }
    ];

    // 验证每条数据
    const results = validDataArray.map(data => feedCitySchema.safeParse(data));

    // 所有数据应该都通过验证
    expect(results.every(result => result.success)).toBe(true);
  });

  // 空对象测试
  it('应该在传入空对象时验证失败', () => {
    const emptyData = {};

    const result = feedCitySchema.safeParse(emptyData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.length).toBeGreaterThan(0);
    }
  });

  // 额外字段测试
  it('应该允许额外的字段存在', () => {
    const dataWithExtraFields = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 'huanya',
      cityName: '上海',
      cityNameTranslated: 'Shanghai',
      language: 'ZH_CN',
      extraField1: 'extra value 1',
      extraField2: 'extra value 2'
    };

    const result = feedCitySchema.safeParse(dataWithExtraFields);
    expect(result.success).toBe(true);
  });

  // 测试 cityName 和 cityNameTranslated 为空字符串时的验证
  it('应该在 cityName 和 cityNameTranslated 为空字符串时验证失败', () => {
    const emptyOptionalStringData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      countryId: 'CN',
      cityId: 'SHA',
      vendorType: 'huanya',
      cityName: '', // 空字符串
      cityNameTranslated: '', // 空字符串
      language: 'ZH_CN'
    };

    const result = feedCitySchema.safeParse(emptyOptionalStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('cityName'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('cityNameTranslated'))).toBe(true);
    }
  });

  // 测试不同语言的支持
  it('应该支持不同的语言', () => {
    const validLanguages = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        cityId: 'SHA',
        vendorType: 'huanya',
        cityName: '上海',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        cityId: 'SHA',
        vendorType: 'huanya',
        cityName: 'Shanghai',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=JA_JP\u0001env=prod',
        vendorCode: 'huanya',
        countryId: 'CN',
        cityId: 'SHA',
        vendorType: 'huanya',
        cityName: '上海',
        language: 'JA_JP'
      }
    ];

    const results = validLanguages.map(data => feedCitySchema.safeParse(data));
    expect(results.every(result => result.success)).toBe(true);
  });
});
