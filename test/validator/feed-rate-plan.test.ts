import feedRatePlanSchema from '../../src/validator/schemas/feed-rate-plan.schema';

describe('feed-rate-plan.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '小城故事大床房(标准价)',
      paymentType: 'PREPAY',
      priceType: 'COST',
      meal: [
        {
          calendaring: false,
          number: 0,
          type: 'ROOM_ONLY'
        }
      ],
      cancelPolicies: [
        {
          cancelPolicyType: 'LIMITED_TIME',
          details: [
            {
              advanceHours: 4,
              rate: 0
            }
          ]
        }
      ],
      status: true,
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      vendorCode: 'huanya',
      hotelId: '5699988',
      // 缺少 requestPk 和 ratePlanId
      ratePlanName: '小城故事大床房(标准价)',
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      // 验证错误信息包含缺失字段
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('ratePlanId'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidEnumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      paymentType: 'INVALID_PAYMENT_TYPE', // 无效的支付类型
      priceType: 'COST',
      language: 'INVALID_LANGUAGE' // 无效的语言
    };

    const result = feedRatePlanSchema.safeParse(invalidEnumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('paymentType'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('language'))).toBe(true);
    }
  });

  // 嵌套对象验证测试
  it('应该验证嵌套对象的有效性', () => {
    const invalidNestedData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      meal: [
        {
          calendaring: 'not-a-boolean', // 应该是布尔值
          number: 'not-a-number', // 应该是数字
          type: 'INVALID_MEAL_TYPE' // 无效的餐食类型
        }
      ],
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(invalidNestedData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('meal'))).toBe(true);
    }
  });

  // 取消政策验证测试
  it('应该验证取消政策的有效性', () => {
    const invalidCancelPolicyData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      cancelPolicies: [
        {
          cancelPolicyType: 'INVALID_POLICY_TYPE', // 无效的取消政策类型
          details: [
            {
              advanceHours: 'not-a-number', // 应该是数字
              rate: 'not-a-number' // 应该是数字
            }
          ]
        }
      ],
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(invalidCancelPolicyData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('cancelPolicies'))).toBe(true);
    }
  });

  // 类型转换测试
  it('应该正确处理可转换的类型', () => {
    const convertibleData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      status: 'true', // 字符串 'true' 应该可以转换为布尔值 true
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(convertibleData);
    // Zod 默认不会自动转换类型，所以这里应该失败
    expect(result.success).toBe(false);
  });

  // 多条数据批量验证测试
  it('应该能够批量验证多条数据', () => {
    // 解析提供的 JSON 字符串
    const ratePlansData = JSON.parse('[{"requestPk":"vendorCode=huanya\\u0001language=ZH_CN\\u0001env=prod","vendorCode":"huanya","vendorType":"huanya","language":"ZH_CN","hotelId":"5699988","ratePlanId":"hy_5699988_765738_48618208","ratePlanName":"小城故事大床房(标准价)","channel":"O","status":true,"paymentType":"PREPAY","priceType":"COST","stayDate":{"start":"2024-06-01","end":"2999-12-31"},"bookingDate":{"date":{"start":"2024-06-01","end":"2999-12-31"}},"bookingRule":{"minLos":1,"maxLos":90,"minRoomQuantity":1,"maxRoomQuantity":5,"minAdvanceHours":0,"maxAdvanceHours":2161},"meal":[{"calendaring":false,"number":0,"type":"ROOM_ONLY"}],"cancelPolicies":[{"cancelPolicyType":"LIMITED_TIME","details":[{"advanceHours":4,"rate":0}]}],"chanelCommissions":[{"saleChannel":"O","isCommission":false}],"companyAssist":false}]');

    // 验证每条数据
    const results = ratePlansData.map(data => feedRatePlanSchema.safeParse(data));

    // 所有数据应该都通过验证
    expect(results.every(result => result.success)).toBe(true);
  });

  // 边界值测试
  it('应该处理边界值', () => {
    const edgeCaseData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      meal: [
        {
          number: -1, // 负数
          type: 'ROOM_ONLY'
        }
      ],
      cancelPolicies: [
        {
          cancelPolicyType: 'LIMITED_TIME',
          details: [
            {
              advanceHours: 0, // 边界值
              rate: 101 // 超过100%的比率
            }
          ]
        }
      ],
      language: 'ZH_CN'
    };

    // 这个测试主要是检查 schema 是否对边界值有特殊处理
    // 如果没有特殊限制，这个数据应该是有效的
    const result = feedRatePlanSchema.safeParse(edgeCaseData);
    expect(result.success).toBe(true);
  });

  // 空字符串测试
  it('应该正确处理空字符串', () => {
    const emptyStringData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      ratePlanId: 'hy_5699988_765738_48618208',
      ratePlanName: '', // 空字符串
      language: 'ZH_CN'
    };

    const result = feedRatePlanSchema.safeParse(emptyStringData);
    // 根据 BaseTypes.optionalNonEmptyString 的定义，空字符串应该是无效的
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('ratePlanName'))).toBe(true);
    }
  });
});