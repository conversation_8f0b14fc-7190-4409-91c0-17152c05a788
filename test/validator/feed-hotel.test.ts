import feedHotelSchema from '../../src/validator/schemas/feed-hotel.schema';

describe('feed-hotel.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      hotelName: '上海浦东喜来登由由酒店',
      hotelNameTranslated: 'Sheraton Shanghai Pudong Hotel',
      longitude: '121.5374',
      latitude: '31.2288',
      tel: {
        countryCode: '86',
        areaCode: '21',
        number: '58386888'
      },
      addresses: [
        {
          addressLine: '浦东新区浦建路38号',
          postalCode: '200120',
        }
      ],
      pictures: [
        {
          url: 'https://example.com/hotel1.jpg',
          caption: '酒店外观',
        }
      ],
      facilities: [
        {
          name: 'WIFI',
          description: '免费WiFi',
        }
      ],
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失测试
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      // 缺少 requestPk
      vendorCode: 'huanya',
      // 缺少 hotelId
      vendorType: 'huanya',
      hotelName: '上海浦东喜来登由由酒店',
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelId'))).toBe(true);
    }
  });

  // 字段类型错误测试
  it('应该在字段类型错误时验证失败', () => {
    const invalidTypeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 123, // 应该是字符串
      hotelName: 456, // 应该是字符串
      longitude: 'not-a-number', // 应该是数字字符串
      latitude: 'not-a-number', // 应该是数字字符串
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(invalidTypeData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorType'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelName'))).toBe(true);
    }
  });

  // 嵌套对象验证测试
  it('应该验证嵌套对象的有效性', () => {
    const invalidNestedData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      tel: {},
      addresses: [
        {
          // 缺少必填字段
          postalCode: '200120',
          city: '上海'
        }
      ],
      pictures: [
        {
          // url 是必填的
          caption: '酒店外观',
        }
      ],
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(invalidNestedData);
    expect(result.success).toBe(true);
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidEnumData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      facilities: [
        {
          facilityType: 'INVALID_TYPE', // 无效的设施类型
          facilityName: '免费WiFi',
          chargeable: false
        }
      ],
      language: 'INVALID_LANGUAGE' // 无效的语言
    };

    const result = feedHotelSchema.safeParse(invalidEnumData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('language'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const emptyStringData = {
      requestPk: '',
      vendorCode: '',
      hotelId: '',
      vendorType: 'huanya',
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(emptyStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('requestPk'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelId'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const dataWithOptionalFieldsMissing = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      language: 'ZH_CN'
      // 缺少可选字段 hotelName, hotelNameTranslated, longitude, latitude, tel, addresses, pictures, facilities
    };

    const result = feedHotelSchema.safeParse(dataWithOptionalFieldsMissing);
    expect(result.success).toBe(true);
  });

  // 多条数据批量验证测试
  it('应该能够批量验证多条数据', () => {
    const validDataArray = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        vendorType: 'huanya',
        hotelName: '上海浦东喜来登由由酒店',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699989',
        vendorType: 'huanya',
        hotelName: 'Beijing Grand Hotel',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=JA_JP\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699990',
        vendorType: 'huanya',
        hotelName: '広州ホテル',
        language: 'JA_JP'
      }
    ];

    // 验证每条数据
    const results = validDataArray.map(data => feedHotelSchema.safeParse(data));

    // 所有数据应该都通过验证
    expect(results.every(result => result.success)).toBe(true);
  });

  // 空对象测试
  it('应该在传入空对象时验证失败', () => {
    const emptyData = {};

    const result = feedHotelSchema.safeParse(emptyData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.length).toBeGreaterThan(0);
    }
  });

  // 额外字段测试
  it('应该允许额外的字段存在', () => {
    const dataWithExtraFields = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      hotelName: '上海浦东喜来登由由酒店',
      language: 'ZH_CN',
      extraField1: 'extra value 1',
      extraField2: 'extra value 2'
    };

    const result = feedHotelSchema.safeParse(dataWithExtraFields);
    expect(result.success).toBe(true);
  });

  // 测试 hotelName 和 hotelNameTranslated 为空字符串时的验证
  it('应该在可选字段为空字符串时验证失败', () => {
    const emptyOptionalStringData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      hotelName: '', // 空字符串
      hotelNameTranslated: '', // 空字符串
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(emptyOptionalStringData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('hotelName'))).toBe(true);
      expect(result.error.issues.some(issue => issue.path.includes('hotelNameTranslated'))).toBe(true);
    }
  });

  // 测试经纬度范围验证
  it('应该验证经纬度在有效范围内', () => {
    const invalidLongitudeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      longitude: '190.0', // 超出有效范围 (-180 到 180)
      latitude: '31.2288',
      language: 'ZH_CN'
    };

    const invalidLatitudeData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      longitude: '121.5374',
      latitude: '95.0', // 超出有效范围 (-90 到 90)
      language: 'ZH_CN'
    };

    const longitudeResult = feedHotelSchema.safeParse(invalidLongitudeData);
    expect(longitudeResult.success).toBe(false);

    const latitudeResult = feedHotelSchema.safeParse(invalidLatitudeData);
    expect(latitudeResult.success).toBe(false);
  });

  // 测试自定义验证规则
  it('应该通过自定义验证规则', () => {
    // 导入带有自定义验证规则的 schema


    const validData = {
      requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
      vendorCode: 'huanya',
      hotelId: '5699988',
      vendorType: 'huanya',
      longitude: '121.5374',
      latitude: '31.2288',
      language: 'ZH_CN'
    };

    const result = feedHotelSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 测试不同语言的支持
  it('应该支持不同的语言', () => {
    const validLanguages = [
      {
        requestPk: 'vendorCode=huanya\u0001language=ZH_CN\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        vendorType: 'huanya',
        hotelName: '上海浦东喜来登由由酒店',
        language: 'ZH_CN'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=EN_US\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        vendorType: 'huanya',
        hotelName: 'Sheraton Shanghai Pudong Hotel',
        language: 'EN_US'
      },
      {
        requestPk: 'vendorCode=huanya\u0001language=JA_JP\u0001env=prod',
        vendorCode: 'huanya',
        hotelId: '5699988',
        vendorType: 'huanya',
        hotelName: '上海浦東喜来登由由ホテル',
        language: 'JA_JP'
      }
    ];

    const results = validLanguages.map(data => feedHotelSchema.safeParse(data));
    expect(results.every(result => result.success)).toBe(true);
  });
});