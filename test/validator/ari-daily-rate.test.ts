import ariDailyRateSchema from '../../src/validator/schemas/ari-daily-rate';

describe('ari-daily-rate.schema 测试', () => {
  // 有效数据测试
  it('应该通过有效的数据验证', () => {
    const result = ariDailyRateSchema.safeParse(validData[0]);
    expect(result.success).toBe(true);
  });

  // 必填字段缺失
  it('应该在缺少必填字段时验证失败', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      // 缺少 必填字段 date
      // "date": "2025-05-05",
      "status": true,
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    };

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('date'))).toBe(true);
    }
  });

  // 字段类型错误
  it('应该在字段类型错误时验证失败', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": "true", // status 应该是布尔值
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('status'))).toBe(true);
    }
  });

  // 枚举值测试
  it('应该在枚举值不合法时验证失败', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "INVALID_CNY", //  错误的枚举值
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('currency'))).toBe(true);
    }
  });

  // 空字符串测试
  it('应该在必填字段为空字符串时验证失败', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('vendorCode'))).toBe(true);
    }
  });

  // 可选字段测试
  it('应该允许可选字段缺失', () => {
    const validData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      // 缺失了 可选的 hotelId
      // "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(validData);
    expect(result.success).toBe(true);
  });

  // 整数验证测试
  it('应该验证inventory为整数', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3.5 // 应该是整数
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('inventory'))).toBe(true);
    }
  });

  // 多条数据批量测试
  it('应该能够批量验证多条数据', () => {
    const result = validData.map(data => ariDailyRateSchema.safeParse(data));
    expect(result.every(r => r.success)).toBe(true);
  });

  // rateKey 格式测试
  it('rateKey格式不正确时应该不通过', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      // 错误的rateKey
      "rateKey": "5699988_48618208_2_1_invalid",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('rateKey'))).toBe(true);
    }
  });

  // 日期格式测试
  it('date格式不正确时应该不通过', () => {
    const invalidData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      // 错误的日期格式 
      "date": "INVALID_DATE",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 3
    }

    const result = ariDailyRateSchema.safeParse(invalidData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('date'))).toBe(true);
    }
  });

  // 边界值测试
  it('应该支持inventory为0', () => {
    const edgeCaseData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": 0
    }
    const result = ariDailyRateSchema.safeParse(edgeCaseData);
    expect(result.success).toBe(true);
  });

  // 类型转换测试
  it('inventory为字符串时应该不通过', () => {
    const convertibleData = {
      "requestPk": "vendorCode=huanya\u0001env=prod",
      "vendorCode": "huanya",
      "rateKey": "5699988_48618208_2_1",
      "status": true,
      "date": "2025-05-05",
      "vendorType": "huanya",
      "rateId": "48618208_2_1",
      "hotelId": "5699988",
      "roomTypeId": "765738",
      "ratePlanId": "hy_5699988_765738_48618208",
      "time": 1746431655095,
      "currency": "CNY",
      "inclusiveAmount": "366.00",
      "feesAmount": "0",
      "inventory": "3" // 应该是整数
    }
    const result = ariDailyRateSchema.safeParse(convertibleData);
    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.issues.some(issue => issue.path.includes('inventory'))).toBe(true);
    }
  });
});

// 线上真实数据参考
export const validData = [{
  "requestPk": "vendorCode=huanya\u0001env=prod",
  "vendorCode": "huanya",
  "rateKey": "5699988_48618208_2_1",
  "status": true,
  "date": "2025-05-05",
  "vendorType": "huanya",
  "rateId": "48618208_2_1",
  "hotelId": "5699988",
  "roomTypeId": "765738",
  "ratePlanId": "hy_5699988_765738_48618208",
  "time": 1746431655095,
  "currency": "CNY",
  "inclusiveAmount": "366.00",
  "feesAmount": "0",
  "inventory": 3
},
{
  "requestPk": "vendorCode=huanya\u0001env=prod",
  "vendorCode": "huanya",
  "rateKey": "5699988_48618208_2_2",
  "status": true,
  "date": "2025-05-05",
  "vendorType": "huanya",
  "rateId": "48618208_2_2",
  "hotelId": "5699988",
  "roomTypeId": "765738",
  "ratePlanId": "hy_5699988_765738_48618208",
  "time": 1746431655095,
  "currency": "CNY",
  "inclusiveAmount": "366.00",
  "feesAmount": "0",
  "inventory": 3
}];