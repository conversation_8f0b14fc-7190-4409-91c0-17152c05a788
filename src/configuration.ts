import {
  App,
  Configuration,
  ILifeCycle,
  IMidwayContainer,
  Inject,
  Config,
} from '@midwayjs/core';
import * as egg from '@midwayjs/web';
import * as otel from '@ali/midway-otel';
import * as axios from '@midwayjs/axios';
import * as swagger from '@midwayjs/swagger';
import * as validate from '@midwayjs/validate';
import * as baseMiddleware from '@ali/midway-base-middleware';
import * as bullBoard from '@midwayjs/bull-board';
import * as bullmq from '@midwayjs/bullmq';
import * as cron from '@midwayjs/cron';
import * as aliyunSDK from 'aliyun-sdk';

import { Application } from 'egg';
import { join } from 'path';
import * as TableStore from 'tablestore';
import * as Oss from 'ali-oss';
import IORedis from 'ioredis';

import { MonitorService } from '@/helper/monitor';
import { ClsTracingMiddleware } from '@/middleware/cls-tracing';
import { ShutOffRegisterService } from '@/service/queue/shut-off-register';

@Configuration({
  imports: [
    egg,
    baseMiddleware,
    validate,
    otel,
    axios,
    {
      component: swagger,
      enabledEnvironment: ['local'],
    },
    bullmq,
    bullBoard,
    cron,
  ],
  importConfigs: [join(__dirname, './config')],
})
export class ContainerLifeCycle implements ILifeCycle {
  @Config('ots')
  otsConfig;

  @Config('oss')
  ossConfig;

  @Config('sls')
  slsConfig;

  @Config('redis.client')
  redisConfig;

  @App()
  app: Application;

  @Inject()
  appDir: string;

  @Inject()
  diamondService: baseMiddleware.DiamondService;

  async onConfigLoad() {
    // 监听未捕获的异常事件
    process.on('uncaughtException', err => {
      console.error('Switch Database Uncaught Exception:', err);
    });

    // 监听未处理的 Promise 拒绝事件
    process.on('unhandledRejection', (reason, promise) => {
      console.error('Switch Database Unhandled Rejection:', reason, promise);
    });

    process.on('rejectionHandled', promise => {
      console.error('Switch Database Rejection Handled:', promise);
    });
  }

  async onReady(appContext: IMidwayContainer) {
    this.app.useMiddleware([ClsTracingMiddleware]);
    // 创建共用 ots client
    const ots = new TableStore.Client({
      accessKeyId: this.otsConfig.accessKeyId,
      secretAccessKey: this.otsConfig.accessKeySecret,
      endpoint: `http://${this.otsConfig.instanceName}.cn-zhangjiakou.ots-inner.aliyuncs.com`,
      instancename: this.otsConfig.instanceName,
      maxRetries: this.otsConfig.maxRetries,
    });
    appContext.registerObject('otsClient', ots);

    // 创建共用 oss client
    const oss = new Oss({
      endpoint: this.ossConfig.endpoint,
      accessKeyId: this.ossConfig.accessKeyId,
      accessKeySecret: this.ossConfig.accessKeySecret,
      bucket: this.ossConfig.bucket,
    });
    appContext.registerObject('ossClient', oss);

    // 创建共用 redis client
    const redisClient = new IORedis({
      port: this.redisConfig.port,
      host: this.redisConfig.host,
      password: this.redisConfig.password,
      db: this.redisConfig.db,
      maxRetriesPerRequest: null,
    });
    appContext.registerObject('redis', redisClient);

    const slsReporter = new aliyunSDK.SLS({
      accessKeyId: this.slsConfig.accessKeyId,
      secretAccessKey: this.slsConfig.secretAccessKey,
      endpoint: this.slsConfig.endpoint,
      apiVersion: '2015-06-01', // SDK版本号，固定值，不需要修改
    });
    appContext.registerObject('slsReporter', slsReporter);

    // 启动时上报监控指标
    const monitorService = await appContext.getAsync(MonitorService);
    monitorService.bootstrap();

    const shutOffRegisterService = await appContext.getAsync(ShutOffRegisterService);
    shutOffRegisterService.bootstrap();

    console.log(
      'Midway is running at http://127.0.0.1:7001/swagger-ui/index.html'
    );
  }
}
