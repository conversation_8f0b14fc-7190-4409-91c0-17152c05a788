export const MAX_BATCH_SIZE = 100;

export enum TABLESTORE_ENUM {
  INF_MAX = 'INF_MAX',
  INF_MIN = 'INF_MIN',
}


export const FEED_RATE_PLAN = 'feed_rate_plan';

export const FEED_HOTEL = 'feed_hotel';

export const ARI_RATE = 'ari_rate';

export const ARI_DAILY_RATE = 'ari_daily_rate';

export const ARI_ROOM_INVENTORY = 'ari_room_inventory';

export const FEED_CITY = 'feed_city';

export const FEED_CACHE = 'feed_cache';

export const FEED_COUNTRY = 'feed_country';

export const FEED_HOTEL_COMMENT = 'feed_hotel_comment';

export const FEED_INVOICE = 'feed_invoice';

export const FEED_PROPERTY = 'feed_property';

export const FEED_PROPERTY_RELATION = 'feed_property_relation';

export const FEED_ROOM_TYPE = 'feed_room_type';

export const TABLE_STORE_NAME = {
  FEED_RATE_PLAN,
  FEED_HOTEL,
  ARI_RATE,
  ARI_DAILY_RATE,
  ARI_ROOM_INVENTORY,
  FEED_CITY,
  FEED_CACHE,
  FEED_COUNTRY,
  FEED_HOTEL_COMMENT,
  FEED_INVOICE,
  FEED_PROPERTY,
  FEED_PROPERTY_RELATION,
  FEED_ROOM_TYPE,
}


export enum PROCESS {
  SEND_MESSAGE = 'SEND_MESSAGE',

  OSS_PUT = 'OSS_PUT',

  OSS_GET = 'OSS_GET',

  GET_MESSAGE = 'GET_MESSAGE',

  OTS_BATCH_UPDATE = 'OTS_BATCH_UPDATE',

  OTS_VALIDATE = 'OTS_VALIDATE',

  OTS_BATCH_UPDATE_LIMIT = 'OTS_BATCH_UPDATE_LIMIT',

  WORKER_PROCESS = 'WORKER_PROCESS',

  METRICS_REPORT = 'METRICS_REPORT',

  OTS_BATCH_UPDATE_CALLBACK = 'OTS_BATCH_UPDATE_CALLBACK',

  INNER_ERROR = 'INNER_ERROR',

  API = 'API',


  // 下架相关
  SHUT_OFF_CHECK = 'SHUT_OFF_CHECK',
  SHUT_OFF = 'SHUT_OFF',
  SHUT_OFF_BACKUP = 'SHUT_OFF_BACKUP',


  // 回调相关
  CALLBACK_DISPATCH = 'CALLBACK_DISPATCH',
}


