/**
 * ari_room_inventory 表数据校验器
 */
import { z } from 'zod';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 使用 SchemaFactory 创建 Schema
export const ariRoomInventorySchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.nonEmptyString,
  roomTypeId: BaseTypes.nonEmptyString,
  date: BaseTypes.dateString,

  // 核心字段
  vendorType: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  blockedCount: BaseTypes.optional(BaseTypes.integer),
  count: BaseTypes.optional(BaseTypes.integer),
  disabled: BaseTypes.optional(BaseTypes.boolean)
});

// 添加自定义验证规则
const ariRoomInventorySchemaWithRefinements = ariRoomInventorySchema.refine(
  data => {
    return true;
  },
  {
    message: '预留自定义校验规则，未启用',
    path: ['']
  }
);

// 导出最终 Schema
export default ariRoomInventorySchemaWithRefinements;

// 导出类型
export type AriRoomInventory = z.infer<typeof ariRoomInventorySchemaWithRefinements>;