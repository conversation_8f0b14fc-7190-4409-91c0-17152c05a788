/**
 * feed_country 表数据校验器
 */
import { z } from 'zod';

import { LANGUAGE } from '@ali/switch-hotel-utils';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义语言枚举
const languages = Object.values(LANGUAGE) as [string, ...string[]];


// 使用 SchemaFactory 创建 Schema
export const feedCountrySchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  countryId: BaseTypes.nonEmptyString,

  // 核心字段
  vendorType: BaseTypes.nonEmptyString,
  countryName: BaseTypes.optionalNonEmptyString,
  countryNameTranslated: BaseTypes.optionalNonEmptyString,
  countryIso: BaseTypes.optionalNonEmptyString,
  language: BaseTypes.createEnum(languages),
});

// 添加自定义验证规则
const feedCountrySchemaWithRefinements = feedCountrySchema.refine(
  data => {
    return true;
  },
  {
    message: '预留自定义校验规则，未启用',
    path: ['']
  }
);

// 导出最终 Schema
export default feedCountrySchemaWithRefinements;

// 导出类型
export type FeedCountry = z.infer<typeof feedCountrySchemaWithRefinements>;