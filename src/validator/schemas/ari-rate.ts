/**
 * ari_rate 表数据校验器
 */
import { z } from 'zod';

import { CURRENCY, LANGUAGE } from '@ali/switch-hotel-utils';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义语言枚举
const languages = Object.values(LANGUAGE) as [string, ...string[]];
// 定义货币枚举
const currencies = Object.values(CURRENCY) as [string, ...string[]];

// 使用 SchemaFactory 创建 Schema
export const ariRateSchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.nonEmptyString,
  rateKey: BaseTypes.nonEmptyString,

  // 核心字段
  vendorType: BaseTypes.nonEmptyString,
  currency: BaseTypes.createOptionalEnum(currencies),
  los: BaseTypes.optional(BaseTypes.integer),
  rateId: BaseTypes.optionalNonEmptyString,
  ratePlanId: BaseTypes.optionalNonEmptyString,
  roomInventory: BaseTypes.optional(BaseTypes.boolean),
  roomTypeId: BaseTypes.optionalNonEmptyString,
  status: BaseTypes.optional(BaseTypes.boolean),
  name: BaseTypes.optionalNonEmptyString,
  occupancy: BaseTypes.optional(BaseTypes.integer),
  language: BaseTypes.createOptionalEnum(languages)
});

// 添加自定义验证规则
const ariRateSchemaWithRefinements = ariRateSchema.refine(
  data => {
    // 验证 rateKey 是否符合格式要求
    if (data.rateKey !== `${data.hotelId}_${data.rateId}`) {
      return false;
    }
    return true;
  },
  {
    message: 'rateKey 必须符合格式: hotelId_rateId',
    path: ['rateKey']
  }
);

// 导出最终 Schema
export default ariRateSchemaWithRefinements;

// 导出类型
export type AriRate = z.infer<typeof ariRateSchemaWithRefinements>;