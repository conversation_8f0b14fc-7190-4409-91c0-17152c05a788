/**
 * feed_room_type 表数据校验器
 */
import { z } from 'zod';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义床型关系枚举
const bedRelations = ['OR', 'AND'] as const;
// 床型
const bed = z.object({
  bedType: BaseTypes.nonEmptyString,
  bedNum: BaseTypes.optional(BaseTypes.nonNegativeInteger),
  relation: BaseTypes.createEnum(bedRelations)
});

// 使用 SchemaFactory 创建 Schema
export const feedRoomTypeSchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.nonEmptyString,
  roomTypeId: BaseTypes.nonEmptyString,

  // 核心字段
  vendorType: BaseTypes.nonEmptyString,
  maxOccupancy: BaseTypes.optional(BaseTypes.integer),
  name: BaseTypes.optionalNonEmptyString,
  nameTranslated: BaseTypes.optionalNonEmptyString,
  sizeOfRoom: BaseTypes.optionalNonEmptyString,
  bed: BaseTypes.optional(z.array(bed)),
  bedTypeDesc: BaseTypes.optionalNonEmptyString,
});

// 添加自定义验证规则
const feedRoomTypeSchemaWithRefinements = feedRoomTypeSchema.refine(
  data => {
    return true;
  },
  {
    message: '预留自定义校验规则，未启用',
    path: ['']
  }
);

// 导出最终 Schema
export default feedRoomTypeSchemaWithRefinements;

// 导出类型
export type FeedRoomType = z.infer<typeof feedRoomTypeSchemaWithRefinements>;