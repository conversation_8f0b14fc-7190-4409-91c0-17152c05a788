/**
 * ari_daily_rate 表数据校验器
 */
import { z } from 'zod';

import { CURRENCY } from '@ali/switch-hotel-utils';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义货币枚举
const currencies = Object.values(CURRENCY) as [string, ...string[]];

// 使用 SchemaFactory 创建 Schema
export const ariDailyRateSchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  rateKey: BaseTypes.nonEmptyString,
  date: BaseTypes.dateString,

  // 核心字段
  vendorType: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.optionalNonEmptyString,
  inclusiveAmount: BaseTypes.optionalNonEmptyString,
  currency: BaseTypes.createOptionalEnum(currencies),
  inventory: BaseTypes.optional(BaseTypes.integer),
  rateId: BaseTypes.optionalNonEmptyString,
  ratePlanId: BaseTypes.optionalNonEmptyString,
  roomTypeId: BaseTypes.optionalNonEmptyString,
  status: BaseTypes.optional(BaseTypes.boolean),
});

// 添加自定义验证规则
const ariDailyRateSchemaWithRefinements = ariDailyRateSchema.refine(
  data => {
    // 验证 rateKey 是否符合格式要求
    if (data.hotelId && data.rateId && data.rateKey !== `${data.hotelId}_${data.rateId}`) {
      return false;
    }
    return true;
  },
  {
    message: 'rateKey 必须符合格式: hotelId_rateId',
    path: ['rateKey']
  }
);

// 导出最终 Schema
export default ariDailyRateSchemaWithRefinements;

// 导出类型
export type AriDailyRate = z.infer<typeof ariDailyRateSchemaWithRefinements>;