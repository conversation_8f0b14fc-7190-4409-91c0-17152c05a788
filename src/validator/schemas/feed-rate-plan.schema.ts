/**
 * feed_rate_plan 表数据校验器
 */
import { z } from 'zod';

import { LANGUAGE, PAYMENT_TYPE, PRICE_TYPE, MEALS_TYPE, CANCEL_POLICY_TYPE } from '@ali/switch-hotel-utils';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义语言枚举
const languages = Object.values(LANGUAGE) as [string, ...string[]];
// 定义支付类型枚举
const paymentTypes = Object.values(PAYMENT_TYPE) as [string, ...string[]];
// 定义价格类型枚举
const priceTypes = Object.values(PRICE_TYPE) as [string, ...string[]];
// 定义取消政策类型枚举
const cancelPolicyTypes = Object.values(CANCEL_POLICY_TYPE) as [string, ...string[]];

// 定义餐食 Schema
const mealSchema = z.object({
  calendaring: BaseTypes.optional(BaseTypes.boolean),
  number: BaseTypes.optional(BaseTypes.number),
  type: z.nativeEnum(MEALS_TYPE).optional(),
  extraCharge: BaseTypes.optionalNonEmptyString,
  startDate: BaseTypes.optionalNonEmptyString,
  endDate: BaseTypes.optionalNonEmptyString
});

// 定义取消政策详情 Schema
const cancelPolicyDetailSchema = z.object({
  advanceHours: BaseTypes.optional(BaseTypes.number),
  rate: BaseTypes.optional(BaseTypes.number)
});

// 定义取消政策 Schema
const cancelPolicySchema = z.object({
  cancelPolicyType: BaseTypes.createOptionalEnum(cancelPolicyTypes),
  effectiveStartDate: BaseTypes.optionalNonEmptyString,
  effectiveEndDate: BaseTypes.optionalNonEmptyString,
  details: BaseTypes.optional(z.array(cancelPolicyDetailSchema))
});

// 使用 SchemaFactory 创建 Schema
export const feedRatePlanSchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.nonEmptyString,
  ratePlanId: BaseTypes.nonEmptyString,

  // 核心字段
  ratePlanName: BaseTypes.optionalNonEmptyString,
  paymentType: BaseTypes.createOptionalEnum(paymentTypes),
  priceType: BaseTypes.createOptionalEnum(priceTypes),
  meal: BaseTypes.optional(z.array(mealSchema)),
  cancelPolicies: BaseTypes.optional(z.array(cancelPolicySchema)),
  status: BaseTypes.optional(BaseTypes.boolean),
  language: BaseTypes.createOptionalEnum(languages)
});

// 添加自定义验证规则
const feedRatePlanSchemaWithRefinements = feedRatePlanSchema.refine(
  data => {
    return true;
  },
  {
    message: '预留自定义校验规则，未启用',
    path: ['']
  }
);

// 导出最终 Schema
export default feedRatePlanSchemaWithRefinements;

// 导出类型
export type FeedRatePlan = z.infer<typeof feedRatePlanSchemaWithRefinements>;
