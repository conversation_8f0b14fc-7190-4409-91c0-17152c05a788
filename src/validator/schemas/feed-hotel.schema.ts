/**
 * feed_hotel 表数据校验器
 */
import { z } from 'zod';

import { LANGUAGE } from '@ali/switch-hotel-utils';

import { SchemaFactory, BaseTypes } from '@/validator/schema-factory';

// 定义语言枚举
const languages = Object.values(LANGUAGE) as [string, ...string[]];

// 电话信息
const tel = z.object({
  number: BaseTypes.optionalNonEmptyString,
  countryCode: BaseTypes.optionalNonEmptyString,
  areaCode: BaseTypes.optionalNonEmptyString
});

// 地址信息
const address = z.object({
  addressLine: BaseTypes.optionalNonEmptyString,
  city: BaseTypes.optionalNonEmptyString,
  country: BaseTypes.optionalNonEmptyString,
  addressType: BaseTypes.optionalNonEmptyString,
  postalCode: BaseTypes.optionalNonEmptyString
});

// 图片信息
const picture = z.object({
  id: BaseTypes.optionalNonEmptyString,
  caption: BaseTypes.optionalNonEmptyString,
  captionTranslated: BaseTypes.optionalNonEmptyString,
  url: BaseTypes.optionalNonEmptyString,
  isMainPic: BaseTypes.optional(BaseTypes.boolean)
});

// 设施信息
const facility = z.object({
  id: BaseTypes.optionalNonEmptyString,
  name: BaseTypes.optionalNonEmptyString,
  nameTranslated: BaseTypes.optionalNonEmptyString,
  description: BaseTypes.optionalNonEmptyString
});

// 使用 SchemaFactory 创建 Schema
export const feedHotelSchema = SchemaFactory.create({
  // 主键
  requestPk: BaseTypes.nonEmptyString,
  vendorCode: BaseTypes.nonEmptyString,
  hotelId: BaseTypes.nonEmptyString,

  // 核心字段
  vendorType: BaseTypes.optionalNonEmptyString,
  hotelName: BaseTypes.optionalNonEmptyString,
  hotelNameTranslated: BaseTypes.optionalNonEmptyString,
  longitude: BaseTypes.optionalNonEmptyString,
  latitude: BaseTypes.optionalNonEmptyString,
  tel: BaseTypes.optional(tel),
  addresses: BaseTypes.optional(z.array(address)),
  pictures: BaseTypes.optional(z.array(picture)),
  facilities: BaseTypes.optional(z.array(facility)),
  language: BaseTypes.createOptionalEnum(languages),
});

// 添加自定义验证规则
const feedHotelSchemaWithRefinements = feedHotelSchema.refine(
  data => {
    if (data.longitude && (Number(data.longitude) < -180 || Number(data.longitude) > 180)) {
      return false;
    }

    if (data.latitude && (Number(data.latitude) < -90 || Number(data.latitude) > 90)) {
      return false;
    }
    return true;
  },
  {
    message: '经纬度必须在有效范围内 (经度: -180 到 180, 纬度: -90 到 90)',
    path: ['longitude', 'latitude']
  }
);

// 导出最终 Schema
export default feedHotelSchemaWithRefinements;

// 导出类型
export type FeedHotel = z.infer<typeof feedHotelSchemaWithRefinements>;