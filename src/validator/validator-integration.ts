/**
 * OTS 服务与验证器集成
 */
import { Provide, Inject } from '@midwayjs/core';

import { ValidatorConfigManager } from '@/validator/config-manager';
import { LogService } from '@/helper/log';
import { BatchValidationResult, ZodValidator } from '@/validator/zod-validator';
import { PROCESS } from '@/constants';

@Provide()
export class OtsValidatorIntegration {
  @Inject()
  zodValidator: ZodValidator;

  @Inject()
  configManager: ValidatorConfigManager;

  @Inject()
  logger: LogService;

  /**
   * 验证并处理 OTS 更新数据
   * @param tableName 表名
   * @param data 数据列表
   */
  async validateOtsData(tableName: string, data: any[]) {
    // 检查表是否启用验证
    const { enable } = await this.configManager.getTableConfig(tableName);
    if (!enable) {
      return { action: 'continue', data };
    }

    // 无校验schema，跳过校验
    if (!this.zodValidator.getSchema(tableName)) {
      return { action: 'continue', data };
    }

    // 执行批量验证
    const result = await this.zodValidator.validateBatch(tableName, data);

    // 处理验证结果
    return await this.handleValidationResult(result, tableName);
  }


  /**
   * 依据配置，决定校验后处理规则
   */
  async handleValidationResult(result: BatchValidationResult, tableName: string): Promise<{ action: 'continue' | 'abort', data?: any[] }> {
    const { valid, validData, invalidItems, data: originData } = result;
    if (!valid) {
      // 1.先记录校验失败的数据
      this.logger.error({
        process: PROCESS.OTS_VALIDATE,
        tableName,
        invalidItems: invalidItems.map(item => ({
          index: item.index,
          errors: item.errors
        }))
      });

      // 2.根据配置决定如何校验失败后续处理动作
      const { failStrategy } = await this.configManager.getTableConfig(tableName);
      // 中止整个表的更新
      if (failStrategy === 'abort') {
        return { action: 'abort' };
      } else if (failStrategy === 'skip') {
        // 只处理有效的数据
        if (validData.length === 0) {
          return { action: 'abort' };
        }
        return { action: 'continue', data: validData };
      } else {
        // 不做处理
        return { action: 'continue', data: originData };
      }
    }

    return { action: 'continue', data: originData };
  }
}