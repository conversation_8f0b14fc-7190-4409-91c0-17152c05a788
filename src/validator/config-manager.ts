import { Provide, Inject, Scope, Scope<PERSON>num, DataListener } from '@midwayjs/core';
import { DiamondService } from '@ali/midway-base-middleware';

@Provide()
@Scope(ScopeEnum.Singleton)
export class ValidatorConfigManager extends DataListener<ValidatorConfig> {
  @Inject()
  diamondService: DiamondService;
  
  private dataId = 'switch-database-validator-config';
  private groupId = 'switch-database';
  
  /**
   * 初始化配置，单例模式，全局只调用一次
   * @returns ValidatorConfig
   */
  async initData() {
    const config = await this.diamondService.getConfig(this.dataId, this.groupId);
    
    try {
      return JSON.parse(config) || DefaultValidatorConfig;
    } catch (err) {
      console.error(`[Init] Parse ots validator config error: ${err.message}. Diamond config: ${config}`);
      return DefaultValidatorConfig;
    }
  }
  
  /**
   * 数据更新
   * @param setData 
   */
  onData(setData: (data: ValidatorConfig) => void) {
    this.diamondService.subscribe({
      dataId: this.dataId,
      group: this.groupId
    }, (content) => {
      try {
        if (content) {
          const config = JSON.parse(content);
          setData(config);
        }
      } catch (err) {
        console.error(`[Update] Parse  ots validator config error: ${err.message}. Diamond config: ${content}`);
      }
    });
  }

  /**
   * 获取表配置
   * @param tableName 
   */
  async getTableConfig(tableName:string) { 
    const config = await this.getData();
    const { enable, failStrategy, tableConfigs } = config; 
    const tableConfig = tableConfigs[tableName] || {} ;
    return {
      enable: tableConfig?.enable ?? enable,
      failStrategy: tableConfig?.failStrategy ?? failStrategy
    };
  }
}


export interface ValidatorConfig {
  /**
   * 全局开关，控制是否开启，优先级低于 tablesConfigs
   */
  enable: boolean;

  /**
   * 校验失败后续处理策略
   * skip: 跳过失败的数据，继续存储
   * abort: 中止整个表的更新
   * log: 只记录错误，但继续使用原始数据存储
   */
  failStrategy: 'skip' | 'abort' | 'log';
  /**
   * 表维度特定配置
   */
  tableConfigs: Record<string, {
    enable?: boolean;
    failStrategy?: 'skip' | 'abort' | 'log';
  }>;
}

export const DefaultValidatorConfig : ValidatorConfig = {
  enable: true,
  failStrategy: 'log',
  tableConfigs: {}
}