/**
 * Schema 工厂，提供常用的 Schema 类型和创建方法
 */
import { z } from 'zod';

/**
 * 基础类型定义
 */
export const BaseTypes = {
  // 字符串类型
  string: z.string(),
  nonEmptyString: z.string().min(1, '不能为空'),
  optionalNonEmptyString: z.string().min(1, '如果提供，不能为空').optional(),
  
  email: z.string().email('邮箱格式不正确'),
  url: z.string().url('URL 格式不正确'),
  
  // 数字类型
  number: z.number(),
  positiveNumber: z.number().positive('必须为正数'),
  nonNegativeNumber: z.number().min(0, '不能为负数'),
  integer: z.number().int('必须为整数'),
  positiveInteger: z.number().int('必须为整数').positive('必须为正整数'),
  nonNegativeInteger: z.number().int('必须为整数').min(0, '不能为负数'),
  
  // 布尔类型
  boolean: z.boolean(),
  
  // 日期类型
  date: z.date(),
  dateString: z.string().refine(
    (val) => !isNaN(Date.parse(val)),
    '日期格式不正确'
  ),
  dateTimeString: z.string().refine(
    (val) => !isNaN(Date.parse(val)),
    '日期时间格式不正确'
  ),
  
  // 数组类型
  array: z.array(z.any()),
  nonEmptyArray: z.array(z.any()).min(1, '不能为空数组'),
  
  // 对象类型
  object: z.object({}),
  record: z.record(z.any()),
  
  // 创建可选类型
  optional: <T extends z.ZodType>(schema: T) => schema.optional(),
  
  // 创建可为空类型
  nullable: <T extends z.ZodType>(schema: T) => schema.nullable(),
  
  // 创建枚举类型
  createEnum: <T extends readonly [string, ...string[]]>(
    values: T,
    errorMessage?: string
  ) => z.enum(values, {
    errorMap: () => ({ message: errorMessage || `必须是以下值之一: ${values.join(', ')}` })
  }),
  
  // 创建可选枚举类型
  createOptionalEnum: <T extends readonly [string, ...string[]]>(
    values: T,
    errorMessage?: string
  ) => z.enum(values, {
    errorMap: () => ({ message: errorMessage || `必须是以下值之一: ${values.join(', ')}` })
  }).optional(),
  
  // 创建数字范围类型
  createNumberRange: (min: number, max: number, errorMessage?: string) => 
    z.number().min(min, errorMessage || `不能小于 ${min}`).max(max, errorMessage || `不能大于 ${max}`),
  
  // 创建字符串长度范围类型
  createStringLength: (min: number, max: number, errorMessage?: string) => 
    z.string().min(min, errorMessage || `长度不能小于 ${min}`).max(max, errorMessage || `长度不能大于 ${max}`),
  
  // 创建正则表达式类型
  createRegex: (regex: RegExp, errorMessage: string) => 
    z.string().regex(regex, errorMessage),
  
  // 创建自定义校验类型
  createCustom: <T extends z.ZodType>(
    schema: T,
    validator: (val: z.infer<T>) => boolean,
    errorMessage: string
  ) => schema.refine(validator, errorMessage)
};

/**
 * Schema 工厂，用于创建复杂的 Schema
 */
export class SchemaFactory {
  /**
   * 创建对象 Schema,使用被动模式
   * @param shape 对象结构
   */
  static create<T extends z.ZodRawShape>(shape: T): z.ZodObject<T> {
    return z.object(shape).passthrough();
  }
  
  /**
   * 创建带有默认值的 Schema
   * @param schema 基础 Schema
   * @param defaultValue 默认值
   */
  static withDefault<T extends z.ZodType>(schema: T, defaultValue: z.infer<T>): z.ZodDefault<T> {
    return schema.default(defaultValue);
  }
  
  /**
   * 创建带有转换的 Schema
   * @param schema 基础 Schema
   * @param transform 转换函数
   */
  static withTransform<T extends z.ZodType, U>(
    schema: T,
    transform: (val: z.infer<T>) => U
  ): z.ZodEffects<T, U> {
    return schema.transform(transform);
  }
  
  /**
   * 创建带有预处理的 Schema
   * @param schema 基础 Schema
   * @param preprocess 预处理函数
   */
  static withPreprocess<T extends z.ZodType>(
    schema: T,
    preprocess: (val: unknown) => unknown
  ): z.ZodEffects<T> {
    return z.preprocess(preprocess, schema);
  }
  
  /**
   * 创建联合类型 Schema
   * @param schemas Schema 列表
   */
  static union<T extends [z.ZodType, z.ZodType, ...z.ZodType[]]>(
    schemas: T
  ): z.ZodUnion<T> {
    return z.union(schemas);
  }
  
  /**
   * 创建交叉类型 Schema
   * @param left 左侧 Schema
   * @param right 右侧 Schema
   */
  static intersection<T extends z.ZodType, U extends z.ZodType>(
    left: T,
    right: U
  ): z.ZodIntersection<T, U> {
    return z.intersection(left, right);
  }
}