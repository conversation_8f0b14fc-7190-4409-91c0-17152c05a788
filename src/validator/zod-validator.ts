/**
 * Zod 校验核心实现
 */
import { Init, Inject, Provide, Scope, ScopeEnum } from '@midwayjs/core';

import { z } from 'zod';

import { ValidatorConfigManager } from '@/validator/config-manager';
import { tableSchemas } from '@/validator/schemas';

@Provide()
@Scope(ScopeEnum.Singleton)
export class ZodValidator {

  @Inject()
  validatorConfigManager: ValidatorConfigManager;

  /**
   * Schema 注册表
   * key 为表名，value 为 Schema
   */
  private schemas: Map<string, z.ZodType> = new Map();

  /**
   * 初始化，注册所有校验 schema
   */
  @Init()
  async init() {
    // 注册所有表的 Schema
    Object.entries(tableSchemas).forEach(([tableName, schema]) => {
      this.registerSchema(
        tableName,
        schema
      );
    });
  }

  /**
   * 注册 Schema
   * @param name Schema 名称，通常是表名
   * @param schema Zod Schema
   * @param options 验证选项
   */
  registerSchema(name: string, schema: z.ZodType): void {
    this.schemas.set(name, schema);
  }

  /**
   * 获取 Schema
   * @param name Schema 名称
   */
  getSchema(name: string): z.ZodType | undefined {
    return this.schemas.get(name);
  }

  /**
   * 校验单条数据
   * @param tableName 表名 名称
   * @param data 待校验数据
   */
  async validate<T = any>(
    tableName: string,
    data: any,
  ): Promise<ValidationResult<T>> {
    const schema = this.schemas.get(tableName);

    if (!schema) {
      throw new Error(`Schema for table "${tableName}" not found`);
    }
    // 获取表维度 校验配置
    const { enable } = await this.validatorConfigManager.getTableConfig(tableName);

    if (!enable) {
      return {
        valid: true,
        data,
        errors: [],
        rawErrors: []
      };
    }

    // 执行校验
    const result = schema.safeParse(data);

    if (result.success) {
      return {
        valid: true,
        data: result.data as T,
        errors: [],
        rawErrors: []
      };
    } else {
      // 处理错误
      const errors = result.error.errors.map(err => {
        const path = err.path.join('.');
        return `${path ? path + ': ' : ''}${err.message}`;
      });

      return {
        valid: false,
        data: result.data,
        errors,
        rawErrors: [result.error]
      };
    }
  }

  /**
   * 批量校验数据
   * @param schemaName Schema 名称
   * @param dataList 待校验数据列表
   * @param options 验证选项，会覆盖注册时的选项
   */
  async validateBatch<T = any>(
    schemaName: string,
    dataList: any[],
  ): Promise<BatchValidationResult<T>> {
    const validData: T[] = [];
    const invalidItems: BatchValidationResult['invalidItems'] = [];

    for (let i = 0; i < dataList.length; i++) {
      const item = dataList[i];
      const result = await this.validate<T>(schemaName, item);

      if (result.valid) {
        validData.push(result.data);
      } else {
        invalidItems.push({
          index: i,
          data: item,
          errors: result.errors,
          rawErrors: result.rawErrors
        });
      }
    }

    return {
      valid: invalidItems.length === 0,
      validData,
      invalidItems,
      data: dataList
    };
  }
}



// 单条校验结果
export interface ValidationResult<T = any> {
  valid: boolean;
  data: T;
  errors: string[];
  rawErrors: z.ZodError[];
}

// 批量校验结果
export interface BatchValidationResult<T = any> {
  valid: boolean;
  validData: T[];
  invalidItems: {
    index: number;
    data: any;
    errors: string[];
    rawErrors: z.ZodError[];
  }[];
  data: T[];
}