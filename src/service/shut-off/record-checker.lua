-- 使用lua脚本用于保证多次操作的原子性

local key = KEYS[1]
local request = ARGV[1]
local threshold = tonumber(ARGV[2])

-- 获取队列最新数据，判断当前 request是否大于
local lastRequest = redis.call('lindex', key, 0)
-- 确保时间戳使用 number 类型
lastRequest = tonumber(lastRequest)
request = tonumber(request)
if lastRequest and lastRequest >= request then
  return nil
end

-- 添加当前请求到队列头部
redis.call('lpush', key, request)

-- 获取队列长度
local len = redis.call('llen', key)

-- 如果队列长度超过阈值，则移除队列尾部的请求,并将其返回
if len > threshold then
  return redis.call('rpop', key)
-- 否则返回null
else
  return nil
end

