/**
 * 下架逻辑实现
 * 严格限制：调用方必须将此任务放在调度任务结尾进行。任务执行期间不要出现 业务写入与下架任务并存的情况。
 */
import { Provide, Inject } from '@midwayjs/core';
import { Redis } from '@midwayjs/redis';

import * as fse from 'fs-extra';
import * as Tablestore from 'tablestore';

import { LogService } from '@/helper/log';
import { PROCESS, MAX_BATCH_SIZE } from '@/constants';
import { ShutOffVendorTableDTO } from '@/dto/shut-off';
import { safeJsonParse } from '@/utils/json';
import { OssService } from '@/helper/oss';
import { CallbackService } from '@/helper/callback';

const luaScript = fse
  .readFileSync(__dirname + '/record-checker.lua')
  .toString();

@Provide()
export class ShutOffService {
  @Inject('redis')
  redis: Redis;

  @Inject('otsClient')
  otsClient: Tablestore.Client;

  @Inject()
  logService: LogService;

  @Inject()
  ossService: OssService;

  @Inject()
  callbackService: CallbackService;

  /**
   * 五次全量未更新则下架
   */
  private readonly FULL_UPDATE_THRESHOLD = 5;

  /**
   * 下架阈值，超出则触发审批
   */
  private readonly SHUT_OFF_RATIO = 0.1;

  /**
   * 下架请求
   * @param key 队列键
   * @param request 请求内容
   * @param threshold 阈值
   * @returns 被下架的请求内容
   */
  async shutOff({
    vendorCode,
    tableName,
    time,
    callbackUrl,
  }: ShutOffVendorTableDTO) {
    const key = `${vendorCode}-${tableName}`;
    const result = await this.redis.eval(
      luaScript,
      1,
      key,
      time,
      this.FULL_UPDATE_THRESHOLD
    );

    if (!result) {
      const message = `本次请求未发现存在 ${this.FULL_UPDATE_THRESHOLD}次全量未更新数据，暂不处理`;
      this.logService.info({
        process: PROCESS.SHUT_OFF_CHECK,
        message: message,
      });
      return message;
    }

    // 查询满足条件的 数据数量
    const searchParams = {
      tableName: 'row_history_cache',
      indexName: 'row_history_cache_index',
      searchQuery: {
        offset: 0,
        limit: 0,
        getTotalCount: true,
        query: {
          queryType: Tablestore.QueryType.BOOL_QUERY,
          query: {
            // 必须同时满足
            mustQueries: [
              {
                queryType: Tablestore.QueryType.TERM_QUERY,
                query: {
                  fieldName: 'vendor_code',
                  term: vendorCode,
                },
              },
              {
                queryType: Tablestore.QueryType.TERM_QUERY,
                query: {
                  fieldName: 'table_name',
                  term: tableName,
                },
              },
              {
                queryType: Tablestore.QueryType.RANGE_QUERY,
                query: {
                  fieldName: 'time',
                  rangeFrom: 0,
                  includeLower: true,
                  rangeTo: time,
                  includeUpper: false,
                },
              },
            ],
          },
        },
      },
      columnToGet: {
        returnType: Tablestore.ColumnReturnType.RETURN_NONE,
      },
    };

    const resp = await this.otsClient.search(searchParams);

    // 满足条件的格式
    // @ts-ignore
    const satisfiedCount = resp.totalCounts;

    console.log('Search Resp:', satisfiedCount);

    // 查询总数量
    const totalCount = await this.getBizTableCount(tableName, vendorCode);

    console.log('Total Count:', totalCount);

    // 计算阈值，如果超出则进入审批状态
    const ratio = satisfiedCount / totalCount;
    if (ratio > this.SHUT_OFF_RATIO) {
      const message = `下架比例超过阈值，请注意核实。下架数量 ${satisfiedCount},总数量 ${totalCount}`;
      this.logService.info({
        process: PROCESS.SHUT_OFF_CHECK,
        message: message,
      });
    }

    // 执行下架逻辑
    const shutOffResult = await this.shutOffRows({
      vendorCode,
      tableName,
      time,
    });

    this.logService.info({
      process: PROCESS.SHUT_OFF,
      message: `下架成功，共下架 ${shutOffResult.rows.length} 条数据`,
    });

    // 备份至 OSS
    const { name } = await this.ossService.put(
      JSON.stringify(shutOffResult),
      `shut-off/${vendorCode}/${tableName}/${time}`
    );
    this.logService.info({
      process: PROCESS.SHUT_OFF_BACKUP,
      message: `下架数据备份成功，文件名: ${name}`,
    });

    // 回调用户，告知结果
    await this.callbackService.dispatch(callbackUrl, shutOffResult, {
      jobName: 'shut-off',
    });

    return '下架任务已完成，请注意观察数据是否符合预期';
  }

  /**
   * 获取业务表中的总个数
   */
  async getBizTableCount(tableName: string, vendorCode: string) {
    const params = {
      tableName: tableName,
      indexName: `${tableName}_index`,
      searchQuery: {
        offset: 0,
        limit: 0,
        getTotalCount: true,
        query: {
          queryType: Tablestore.QueryType.TERM_QUERY,
          query: {
            fieldName: 'vendor_code',
            term: vendorCode,
          },
        },
      },
      columnToGet: {
        returnType: Tablestore.ColumnReturnType.RETURN_NONE,
      },
    };

    const resp = await this.otsClient.search(params);

    // @ts-ignore
    return resp.totalCounts;
  }

  /**
   * 下架表数据
   */
  async shutOffRows({
    vendorCode,
    tableName,
    time,
  }: Pick<ShutOffVendorTableDTO, 'vendorCode' | 'tableName' | 'time'>) {
    let startPrimaryKey = [
      {
        vendor_code: vendorCode,
      },
      {
        table_name: tableName,
      },
      {
        request_id: Tablestore.INF_MIN,
      },
    ] as [{ vendor_code: string }, { table_name: string }, { request_id: any }];

    const shutoffRows = [];

    while (startPrimaryKey !== null) {
      const getRangeParams: Tablestore.GetRangeParams = {
        tableName: 'row_history_cache',
        direction: Tablestore.Direction.FORWARD,
        limit: MAX_BATCH_SIZE,
        maxVersions: 1,
        inclusiveStartPrimaryKey: startPrimaryKey,
        exclusiveEndPrimaryKey: [
          {
            vendor_code: vendorCode,
          },
          {
            table_name: tableName,
          },
          {
            request_id: Tablestore.INF_MAX,
          },
        ],
        columnFilter: new Tablestore.SingleColumnCondition(
          'time',
          Tablestore.Long.fromNumber(time),
          Tablestore.ComparatorType.LESS_EQUAL,
          false
        ),
      };

      const resp = await this.otsClient.getRange(getRangeParams);

      const { rows, nextStartPrimaryKey } = resp;

      // 组装下架数据
      const updateRows = [];
      const now = Date.now();
      for (let i = 0; i < rows.length; i++) {
        const row = rows[i];
        const { attributes } = row;

        // 获取rowMeta 数据
        let rowMeta = null;
        for (let i = 0; i < attributes.length; i++) {
          const attr = attributes[i];
          if (attr.columnName === 'row_meta') {
            rowMeta = safeJsonParse(`${attr.columnValue}`);
            break;
          }
        }

        if (rowMeta) {
          const primaryKey = Object.entries(rowMeta).map(([key, value]) => ({
            [key]:
              typeof value === 'number'
                ? Tablestore.Long.fromNumber(value)
                : value,
          }));
          updateRows.push({
            type: 'UPDATE',
            condition: new Tablestore.Condition(
              Tablestore.RowExistenceExpectation.EXPECT_EXIST,
              null
            ),
            primaryKey,
            attributeColumns: [
              {
                PUT: [
                  { status: false },
                ],
              },
            ],
            returnContent: {
              returnType: Tablestore.ReturnType.Primarykey,
            },
          });
        }
      }

      const batchWriteParams = {
        tables: [
          {
            tableName,
            rows: updateRows,
          },
        ],
      };

      const { tables } = await this.otsClient.batchWriteRow(batchWriteParams);

      tables.forEach((row, index) => {
        if (!row.isOk) {
          console.error('Batch write row error: ', row.errorMessage);
        }
        shutoffRows.push(updateRows[index].primaryKey);
      });

      if (nextStartPrimaryKey) {
        startPrimaryKey = [
          {
            vendor_code: vendorCode,
          },
          {
            table_name: tableName,
          },
          {
            request_id: nextStartPrimaryKey[2].value,
          },
        ];
      } else {
        startPrimaryKey = null;
      }
    }

    // 备份下架数据
    return {
      vendorCode,
      tableName,
      rows: shutoffRows,
    };
  }
}
