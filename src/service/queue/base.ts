/**
 * 队列基类，提供数据压缩能力，子类只需实现具体的任务注册逻辑
 */
import { Inject } from '@midwayjs/core';
import * as bullmq from '@midwayjs/bullmq';
import * as bullBoard from '@midwayjs/bull-board';
import { Redis } from '@midwayjs/redis';

import { v4 as uuidv4 } from 'uuid';

import { LogService } from '@/helper/log';
import { env } from '@/utils/ots';
import { CommonParamsDTO } from '@/dto/common';
import { OssService } from '@/helper/oss';
import { BULL_BOARD_REGISTER_JOBS } from '@/helper/bull-board';
import { ConfigServerService, DefaultBullmqConfig } from '@/helper/config-server';
import { getPrefix } from '@/utils/bull-mq';
import { PROCESS } from '@/constants';


export class AbstractBaseQueueService {
  @Inject('redis')
  redis: Redis;

  @Inject()
  logger: LogService;

  @Inject()
  ossService: OssService;

  @Inject()
  bullmqFramework: bullmq.Framework;

  @Inject()
  bullBoardManager: bullBoard.BullBoardManager;

  @Inject()
  configServerService: ConfigServerService;

  /**
   * 最终方案为（参考：https://help.aliyun.com/zh/mns/send-oversized-messages）
   * 1. 在添加任务前,则先将写入数据上传至OSS
   * 2. 将写入数据对应的 OSS Object信息 添加到任务中
   * 3. 任务执行时，从OSS下载数据，进行处理
   */
  async add<T extends CommonParamsDTO>(queueName: string, params: T) {
    try {
      const { vendorCode } = params;
      const uuid = `${params.uuid}_${uuidv4()}`;
      const [_, tableName] = queueName.split('|');
      const { name } = await this.ossService.put(params, `database/${vendorCode}/${env}/${tableName}/${uuid}`);
      const msg = {
        type: 'oss',
        data: name,
      };

      // 在使用 redis集群模式时，由于 bullmq 内部会同时使用多个key进行原子操作。因此需要保证 同一个队列的相关键需要存储在同一个slot上
      // 为什么不在配置文件中直接配置固定的 prefix? 固定的prefix无法充分利用redis集群的多分片特性，容易导致 数据倾斜。 
      const queue = this.bullmqFramework.getQueue(queueName) || this.bullmqFramework.createQueue(queueName, {
        prefix: getPrefix(queueName),
      });
      // 添加到已注册队列
      await this.redis.sadd(BULL_BOARD_REGISTER_JOBS, queueName);

      const config = await this.configServerService.getData();
      const queueConfig = config?.queue || DefaultBullmqConfig.queue;

      await queue.add(uuid, msg, {
        removeOnComplete: {
          count: queueConfig.removeOnComplete
        },
        removeOnFail: {
          count: queueConfig.removeOnFail
        }
      });

      return queue;
    } catch (err) {
      this.logger.error({
        process: PROCESS.INNER_ERROR,
        errorMessage: `Job add failed: ${err.message}`,
      });
      throw new Error(`Job add failed: ${err.message}`);
    }
  }
}
