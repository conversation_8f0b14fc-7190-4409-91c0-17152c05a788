/**
 * 定时任务注册器，拉取 OSS 下架数据，并注册为队列任务
 */
import { Provide, Inject } from '@midwayjs/core';
import * as bullmq from '@midwayjs/bullmq';
import { Redis } from '@midwayjs/redis';

import { getPrefix } from '@/utils/bull-mq';
import { OssService } from '@/helper/oss';
import { WorkerPool } from '@/helper/worker-pool';
import { OtsSyncWorker } from '@/worker/ots-sync';
import { BULL_BOARD_REGISTER_JOBS } from '@/helper/bull-board';
import {
  ConfigServerService,
  DefaultBullmqConfig,
} from '@/helper/config-server';

@Provide()
export class ShutOffRegisterService {
  /**
   * 扫描 OSS 资源包，并将其注册为 同步任务
   */
  private ossScannerQueueName = 'switch-database-oss-scanner';

  /**
   * 同步 OTS 数据
   */
  private tableStoreSyncQueueName = 'switch-database-ots-sync';

  private ossScannerWorkerName = 'oss-scanner-worker';

  @Inject()
  bullmqFramework: bullmq.Framework;

  @Inject()
  ossService: OssService;

  @Inject()
  workerpool: WorkerPool;

  @Inject()
  otsSyncWorker: OtsSyncWorker;

  @Inject()
  configServerService: ConfigServerService;

  @Inject('redis')
  redis: Redis;

  async bootstrap() {
    const queue =
      this.bullmqFramework.getQueue(this.ossScannerQueueName) ||
      this.bullmqFramework.createQueue(this.ossScannerQueueName, {
        prefix: getPrefix(this.ossScannerQueueName),
      });

    // 通过 job scheduler 定时触发
    await queue.upsertJobScheduler(this.ossScannerWorkerName, {
      // 每小时执行一次
      pattern: '0 0 * * * *',
    });

    await queue.getJobScheduler(this.ossScannerWorkerName);

    // 创建 worker 执行
    this.bullmqFramework.createWorker(
      this.ossScannerQueueName,
      async job => {
        const { timestamp } = job;
        // 根据时间戳获取对应的生成目录: 例如2017/01/23/12/00
        const dir = this._getDirByTimestamp(timestamp);
        const files = await this.ossService.scan(dir);
        const otsSyncQueue = await this._getTableStoreSyncQueue();

        const config = await this.configServerService.getData();
        const queueConfig = config?.queue || DefaultBullmqConfig.queue;

        // 将文件注册为队列任务
        for (const file of files) {
          await otsSyncQueue.add(
            file.name,
            {
              file,
            },
            {
              removeOnComplete: {
                count: queueConfig.removeOnComplete,
              },
              removeOnFail: {
                count: queueConfig.removeOnFail,
              },
            }
          );
        }

        // 注册 worker 进行处理
        await this.workerpool.registerWorker(
          this.tableStoreSyncQueueName,
          this.otsSyncWorker.process.bind(this.otsSyncWorker)
        );
      },
      {
        prefix: getPrefix(this.ossScannerQueueName),
      }
    );
  }

  /**
   * 根据时间戳获取scan目录
   * @param timestamp
   * @returns
   */
  private _getDirByTimestamp(timestamp: number) {
    const date = new Date(timestamp);
    // 取前一个小时时间，避免出现跨天、跨月、跨年场景
    date.setHours(date.getHours() - 1);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hour = String(date.getHours()).padStart(2, '0');
    return `sls-log/${year}/${month}/${day}/${hour}`;
  }

  /**
   * 获取 同步OTS 任务队列
   */
  private async _getTableStoreSyncQueue() {
    const queue =
      this.bullmqFramework.getQueue(this.tableStoreSyncQueueName) ||
      this.bullmqFramework.createQueue(this.tableStoreSyncQueueName, {
        prefix: getPrefix(this.tableStoreSyncQueueName),
      });

    await this.redis.sadd(
      BULL_BOARD_REGISTER_JOBS,
      this.tableStoreSyncQueueName
    );
    return queue;
  }
}
