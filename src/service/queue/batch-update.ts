import { Provide, Inject } from '@midwayjs/core';
import * as bullBoard from '@midwayjs/bull-board';

import { AbstractBaseQueueService } from '@/service/queue/base';
import { OTSBatchUpdateDTO, UpdateEntityDTO } from '@/dto/ots-writer';
import { env } from '@/utils/ots';
import { LogService } from '@/helper/log';
import { MonitorService } from '@/helper/monitor';
import { BatchUpdateWorker } from '@/worker/batch-update-ots';
import { WorkerPool } from '@/helper/worker-pool';

@Provide()
export class BatchUpdateQueueService extends AbstractBaseQueueService {

  @Inject()
  logger: LogService

  @Inject()
  monitor: MonitorService

  @Inject()
  batchUpdateWorker: BatchUpdateWorker;

  @Inject()
  bullBoardManager: bullBoard.BullBoardManager;

  @Inject()
  workerPool: WorkerPool;

  async update(params: OTSBatchUpdateDTO) {
    try {
      let { vendorCode, updates } = params;

      updates = this.drainUpdateParams(updates);
      if (updates.length === 0) {
        return {
          message: 'No update data',
        };
      }

      const tableNames = updates.map(update => update.tableName).join(',');
      const queueName = `${vendorCode}_${env}|${tableNames}`;
      // 添加任务到队列
      await this.add(queueName, params);

      // 创建 worker 执行
      await this.workerPool.registerWorker(queueName, this.batchUpdateWorker.process.bind(this.batchUpdateWorker));

      return {
        message: 'Your request has been received, please wait for the result'
      }
    } catch (err) {
      throw new Error(`send message failed: ${err.message}`);
    }
  }

  /**
   * 去除任务中无用的更新，如无更新数据时则不需要添加到队列中
   */
  drainUpdateParams(updates: UpdateEntityDTO[]) {
    const resp = [];
    for (let i = 0; i < updates.length; i++) {
      const update = updates[i];
      // 仅添加有更新数据的任务
      if (update?.data?.length) {
        resp.push(update);
      }
    }
    return resp;
  }
}
