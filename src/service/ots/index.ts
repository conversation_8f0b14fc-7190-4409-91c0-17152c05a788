import { Provide, Inject, sleep } from '@midwayjs/core';

import * as TableStore from 'tablestore';

import { MAX_BATCH_SIZE, PROCESS } from '@/constants';
import { LogService } from '@/helper/log';
import {
  ConfigServerService,
  DefaultBullmqConfig,
} from '@/helper/config-server';
import { MonitorService } from '@/helper/monitor';
import { RateLimiterService } from '@/helper/rate-limiter';
import { tracer } from '@/utils/tracer';
import { isTableStoreLongType } from '@/utils/ots';

@Provide()
export class OtsService {
  @Inject('otsClient')
  otsClient: TableStore.Client;

  @Inject()
  logger: LogService;

  @Inject()
  monitor: MonitorService;

  @Inject()
  rateLimiterService: RateLimiterService;

  @Inject()
  configServerService: ConfigServerService;

  /**
   * 批量更新数据，可根据写入量自动分批
   * 内置条件更新逻辑，只有字段值不相等时才更新
   * @param options
   * @returns
   */
  async batchUpdate(options: IBatchUpdateOpts) {
    const now = Date.now();
    // 成功写入数量
    let successCount = 0;
    // 失败写入数量
    let failCount = 0;
    // 无须更新数量
    let notUpdateCount = 0;
    // 失败原因
    let failReason = [];

    const { rows, tableName, forceUpdate = false } = options;
    const total = rows.length;

    for (let i = 0; i < total; i += MAX_BATCH_SIZE) {
      // 每次写入的数据
      const currentRows = rows.slice(i, i + MAX_BATCH_SIZE);
      try {
        const info = await this._batchUpdate({
          tableName,
          rows: currentRows,
          forceUpdate,
        });
        successCount += info.successCount;
        failCount += info.failCount;
        notUpdateCount += info.notUpdateCount;
        failReason = failReason.concat(info.failReason);

        const requestBody = tracer.get('x-body') || {};
        const { vendorCode } = requestBody;

        const config = await this.configServerService.getData();
        const { otsLimiter } = config || DefaultBullmqConfig;
        // 判断是否开启限流
        if (
          otsLimiter?.enable === true &&
          otsLimiter.tableNames.includes(tableName)
        ) {
          // 检查是否限流
          const bucketKey = `ots:${vendorCode}:${tableName}`;
          const waitTime = await this.rateLimiterService.get(
            bucketKey,
            info.successCount
          );

          if (waitTime > 0) {
            this.logger.info({
              tableName,
              process: PROCESS.OTS_BATCH_UPDATE_LIMIT,
              rateLimit: true,
              waitTime,
            });
            await sleep(waitTime);
          }
        }
      } catch (err) {
        this.logger.error({
          tableName,
          process: PROCESS.OTS_BATCH_UPDATE,
          error: `Error message: ${err.message} . Error stack: ${err.stack}`,
        });
      }
    }

    const duration = Date.now() - now;

    this.logger.info({
      process: PROCESS.OTS_BATCH_UPDATE,
      tableName,
      success: successCount,
      fail: failCount,
      notUpdate: notUpdateCount,
      failReason,
      duration,
    });

    this.monitor.otsUpdateRecord(tableName, successCount, failCount);

    return {
      success: successCount,
      fail: failCount,
      notUpdate: notUpdateCount,
      failReason,
    };
  }

  /**
   * 获取 OTS 范围数据
   * @param options OTS GetRange 参数
   * @returns OTS GetRange 返回结果
   */
  async getRange(
    options: TableStore.GetRangeParams
  ): Promise<TableStore.GetRangeResult> {
    const {
      tableName,
      inclusiveStartPrimaryKey,
      exclusiveEndPrimaryKey,
      maxVersions = 1,
      direction = TableStore.Direction.FORWARD,
    } = options;

    const params = {
      tableName,
      inclusiveStartPrimaryKey,
      exclusiveEndPrimaryKey,
      maxVersions: maxVersions ?? 1,
      limit: MAX_BATCH_SIZE,
      direction: direction ?? TableStore.Direction.FORWARD,
    };

    return this.otsClient.getRange(params);
  }

  /**
   * 获取 OTS 单行数据
   * @param option OTS GetRow 参数
   * @returns OTS GetRow 返回结果
   */
  async getRow(
    option: TableStore.GetRowParams
  ): Promise<TableStore.SingleRowResult> {
    const { tableName, primaryKey } = option;
    return this.otsClient.getRow({ tableName, primaryKey, maxVersions: 1 });
  }

  /**
   * 获取 OTS 表主键
   * @param tableName OTS 表名
   * @returns OTS 表主键
   */
  async getTablePrimaryKeys(tableName: string): Promise<string[]> {
    return new Promise((resolve, reject) => {
      this.otsClient.describeTable({ tableName }, (err, data) => {
        if (err) reject(err);
        const ret = (data?.tableMeta?.primaryKey || []).map(pk => pk.name);
        resolve(ret);
      });
    });
  }

  // ================================================================================
  // Private
  // ================================================================================

  /**
   * 将 OTS 返回的主键转换为原生类型
   * @param primaryKey OTS 返回的主键
   * @returns 原生类型主键
   */
  private _mapResponseTypePrimaryKeys(
    primaryKey: { name: string; value: any }[]
  ) {
    const ret = {};
    primaryKey.forEach(item => {
      ret[item.name] = item.value;
    });
    return ret;
  }

  /**
   * 将原生类型主键转换为 OTS 返回的主键
   * @param primaryKey 原生类型主键
   * @returns OTS 返回的主键
   */
  private _mapOriginTypePrimaryKeys(primaryKey: Record<string, any>[]) {
    const ret = {};
    primaryKey.forEach(item => {
      Object.entries(item).forEach(([k, v]) => {
        ret[k] = v;
      });
    });
    return ret;
  }

  /**
   * 格式化 OTS 主键
   * @param primaryKey OTS 主键
   * @returns 格式化后的主键
   */
  private _formatPrimaryKey(primaryKey: any[]) {
    primaryKey = primaryKey?.map((item: any) => {
      const name = item.name;
      const value = item.value;
      item = { [name]: value };
      return item;
    });
    return primaryKey;
  }

  /**
   * 生成 OTS 属性映射
   * @param attributeColumns OTS 属性列
   * @returns 属性映射
   */
  private _generateAttributesMapping(attributeColumns) {
    const ret = {};
    attributeColumns.forEach(attr => {
      Object.entries(attr).forEach(([k, v]) => {
        // if (k === 'time') return; // 不过滤时间戳
        if (isTableStoreLongType(v)) {
          ret[k] = (v as any).toNumber();
        } else {
          ret[k] = v;
        }
      });
    });
    return ret;
  }

  /**
   * 格式化 OTS 列数据
   * @param columns OTS 列数据
   * @returns 格式化后的列数据
   */
  private _formatColumns(columns: { [key: string]: any }[]) {
    return columns?.map((item: any) => {
      Object.keys(item).forEach(key => {
        if (item[key] == null) {
          throw new Error('column value is null : ' + key);
        }
      });
      return item;
    });
  }

  /**
   * 批量读取 OTS 数据
   * @param option OTS BatchGetRow 参数
   * @returns OTS BatchGetRow 返回结果
   */
  private async _batchGetRow(option: IBatchUpdateOpts) {
    const { tableName, rows } = option;
    const batchGetRowParams = {
      tables: [
        {
          tableName,
          primaryKey: rows.map(row => {
            const primaryKey = row.primaryKey;
            return primaryKey.map(pk => ({
              [pk.name]: pk.value,
            }));
          }),
        },
      ],
    };

    const { tables } = await this.otsClient.batchGetRow(batchGetRowParams);
    return tables?.[0] || [];
  }

  /**
   * OTS Client batchUpdate 方法封装
   * @param options OTS BatchUpdate 参数
   * @param info 更新统计信息
   * @returns
   */
  private async _batchUpdate(
    options: IBatchUpdateOpts,
    info: {
      successCount: number;
      failCount: number;
      notUpdateCount: number;
      failReason: string[];
    } = { successCount: 0, failCount: 0, notUpdateCount: 0, failReason: [] }
  ) {
    const { tableName, rows = [], forceUpdate } = options;

    const updateRows = forceUpdate ? rows : ([] as Record<string, any>[]);

    if (!forceUpdate) {
      const batchGetRowResult = await this._batchGetRow({ tableName, rows });

      // 遍历查询到的结果，由于 batchGetRow 自身按序返回的特性，因此可以与 rows 一一对应
      for (let i = 0; i < batchGetRowResult.length; i++) {
        // 待更新数据
        const row = rows[i];
        const { primaryKey: rowPrimaryKey, attributes: rowAttributes } = row;
        // 查询到的数据
        const result = batchGetRowResult[i];
        const {
          isOk,
          attributes: getRowAttributes,
          primaryKey: getRowPrimaryKey,
        } = result;
        // 判断 主键 是否一致，不一致则直接更新
        if (!this._isPrimaryKeyEqual(rowPrimaryKey, getRowPrimaryKey)) {
          updateRows.push(row);
          continue;
        }

        if (
          isOk &&
          Array.isArray(getRowAttributes) &&
          Array.isArray(rowAttributes)
        ) {
          let shouldUpdate = false;
          // 本次写入数据对象
          const attributesMapping =
            this._generateAttributesMapping(rowAttributes);
          // 查询到的数据对象
          const existingAttributesMap = {};
          for (const attr of getRowAttributes) {
            // if (attr.columnName === 'time') continue;
            existingAttributesMap[attr.columnName] = isTableStoreLongType(
              attr.columnValue
            )
              ? (attr.columnValue as any).toNumber()
              : attr.columnValue;
          }

          // 对比基准：本次要写入的数据
          for (const key in attributesMapping) {
            // 对比时间戳,如果 本次写入数据时间戳 < 表中已存在的数据，则不更新且跳过对比
            if (key === 'time' && Number(existingAttributesMap[key]) >= Number(attributesMapping[key])) {
              shouldUpdate = false;
              break;
            }

            // 本次更新存在 表中不包含的字段时，先设置为true，看是否存在时间比较
            if (existingAttributesMap[key] === undefined) {
              shouldUpdate = true;
              continue;
            }
            // 当 两者都存在同一属性 且 值不同时，先设置为true，看是否存在时间比较
            if (existingAttributesMap[key] !== attributesMapping[key]) {
              shouldUpdate = true;
              continue;
            }
          }

          if (shouldUpdate) {
            updateRows.push(row);
          } else {
            // 记录更新日志
            this._logRowHistory(row, {
              isUpdated: false,
              tableName,
              level: 'INFO',
            });
            info.notUpdateCount++;
          }
        } else {
          // 未查询到数据，直接写入
          updateRows.push(row);
        }
      }
    }

    if (!updateRows.length) {
      return info;
    }

    // 遍历 batchGetRowResult，对比 本次需要更新的 rows，判断是否需要更新
    const params = {
      tables: [
        {
          tableName,
          rows: updateRows.map(row => {
            return {
              type: 'UPDATE' as const,
              // 忽略行是否存在，存在任一字段不相等则更新
              condition: new TableStore.Condition(
                TableStore.RowExistenceExpectation.IGNORE,
                null
              ),
              primaryKey: this._formatPrimaryKey(row.primaryKey),
              returnContent: { returnType: TableStore.ReturnType.Primarykey },
              attributeColumns: [{ PUT: this._formatColumns(row.attributes) }],
            };
          }),
        },
      ],
    };

    try {
      const { tables } = await this.otsClient.batchWriteRow(params);
      // 真实更新成功的行数
      tables.forEach((row, index) => {
        if (row.isOk) {
          // batchWriteRow有序 且 tables 更新不会返回所有数据，这里需要使用 updateRows 上报日志
          this._logRowHistory(updateRows[index], {
            isUpdated: true,
            tableName,
            level: 'INFO',
          });
          info.successCount++;
        } else {
          this._logRowHistory(updateRows[index], {
            isUpdated: false,
            tableName,
            level: 'ERROR',
            errorMessage: row.errorMessage,
          });
          // 写入失败
          info.failCount++;
          info.failReason.push(row.errorMessage);
        }
      });
      return info;
    } catch (err) {
      const shouldDecreaseRow = /exceeded the MaxLength/.test(err.message);
      // 如果因数据过大导致写入失败，需要拆分成两批进行重写，直到写入成功或单条数据依然过大
      if (shouldDecreaseRow && rows.length > 1) {
        const half = Math.ceil(rows.length / 2);
        const left = rows.slice(0, half);
        const right = rows.slice(half);

        await this._batchUpdate(
          {
            tableName,
            rows: left,
          },
          info
        );
        await this._batchUpdate(
          {
            tableName,
            rows: right,
          },
          info
        );
      } else {
        // 记录失败数据量，上报日志
        info.failCount += rows.length;
        info.failReason.push(err.message);
      }
    }
    return info;
  }

  /**
   * 记录字段更新日志
   * @param row
   * @param param1
   */
  private _logRowHistory(
    row,
    {
      isUpdated,
      tableName,
      level,
      errorMessage,
    }: {
      isUpdated: boolean;
      tableName: string;
      level: 'INFO' | 'ERROR';
      errorMessage?: string;
    }
  ) {
    const primaryKey = row.primaryKey;
    const attributes = row.attributes;

    this.logger.sendRowHistoryLog({
      isUpdated,
      tableName,
      level,
      primaryKey: this._mapResponseTypePrimaryKeys(primaryKey),
      attributes: isUpdated
        ? this._generateAttributesMapping(attributes)
        : level === 'ERROR'
          ? errorMessage
          : undefined,
    });
  }

  /**
   * 判断是否两条数据主键是否相等
   */
  private _isPrimaryKeyEqual(
    primaryKey1: Record<string, any>[],
    primaryKey2: Record<string, any>[]
  ) {
    if (
      !primaryKey1 ||
      !primaryKey2 ||
      primaryKey1?.length !== primaryKey2?.length
    ) {
      return false;
    }
    for (let i = 0; i < primaryKey1.length; i++) {
      const pk1 = primaryKey1[i];
      const pk2 = primaryKey2[i];
      if (pk1.name !== pk2.name || pk1.value !== pk2.value) {
        return false;
      }
    }
    return true;
  }
}

export type ITableStoreClientConfig = {
  accessKeyId: string;
  accessKeySecret: string;
  env: string;
};

export type IBatchUpdateOpts = {
  tableName: string;
  rows: {
    primaryKey: Record<string, any>[];
    attributes: Record<string, any>[];
  }[];
  // 是否强制更新
  forceUpdate?: boolean;
};

export type IGetRangeOpts = {
  tableName: string;
  indexTableName?: string;
  primaryKey?: { [key: string]: any }[];
  inclusiveStartPrimaryKey?: { [key: string]: any }[];
  exclusiveEndPrimaryKey?: { [key: string]: any }[];
  maxVersions?: number;
  direction?: keyof typeof TableStore.Direction;
  limit?: number;
};
