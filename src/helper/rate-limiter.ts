/**
 * 令牌桶限流器
 */
import { Provide, Inject } from '@midwayjs/core';
import { Redis } from '@midwayjs/redis';

import * as fse from 'fs-extra';

import { ConfigServerService, DefaultBullmqConfig } from '@/helper/config-server';

const luaScript = fse.readFileSync(__dirname + '/token-bucket.lua').toString();

@Provide()
export class RateLimiterService {
  @Inject('redis')
  redis: Redis;

  @Inject()
  configServerService: ConfigServerService;

  /**
   * 速率限制的速率。
   */
  private rate = DefaultBullmqConfig.otsLimiter.rate;

  /**
   * 令牌桶的容量。
   */
  private capacity = DefaultBullmqConfig.otsLimiter.capacity;



  /**
   * 获取速率限制信息。
   * 
   * @param {string} bucketKey - 桶键。
   * @param {number} requested - 请求的数量。
   * @returns {Promise<number>} - 返回需要等待的时间。
   */
  async get(bucketKey: string, requested: number): Promise<number> {
    const config = await this.configServerService.getData();
    if (config.otsLimiter) {
      this.rate = config.otsLimiter.rate;
      this.capacity = config.otsLimiter.capacity;
    }

    const now = Date.now();
    const result = await this.redis.eval(luaScript, 1, bucketKey, this.rate.toString(), this.capacity.toString(), now.toString(), requested.toString());
    if (result[0] === 1) {
      return 0;
    } else {
      return result[1];
    }
  }
}