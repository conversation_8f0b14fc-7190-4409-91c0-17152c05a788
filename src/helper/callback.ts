/**
 * 统一回调
 */
import { Provide, Inject } from '@midwayjs/core';
import { HttpService } from '@midwayjs/axios';

import { tracer } from '@/utils/tracer';
import { LogService } from '@/helper/log';
import { PROCESS } from '@/constants';

@Provide()
export class CallbackService {
  @Inject()
  httpService: HttpService;

  @Inject()
  logger: LogService;

  async dispatch(url: string, data: any, { jobName }) {
    const validUrl =
      url &&
      /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/.test(
        url
      );
    if (!validUrl) {
      this.logger.info({
        process: PROCESS.CALLBACK_DISPATCH,
        message: `回调地址${url}不合法，跳过回调`,
      });
      return;
    }

    const requestBody = tracer.get('x-body') || {};
    const { traceId, uuid, vendorCode, vendorType, rpcId } = requestBody;

    try {
      const res = await this.httpService.post(url, {
        traceId,
        uuid,
        vendorCode,
        vendorType,
        rpcId,
        databaseResult: data,
        tips: {
          jobName,
        },
      });
      this.logger.info({
        process: PROCESS.CALLBACK_DISPATCH,
        message: `${jobName}处理完成，回调地址: ${url}成功`,
      });
      return res;
    } catch (err) {
      this.logger.error({
        process: PROCESS.CALLBACK_DISPATCH,
        message: `${jobName}处理完成，回调地址: ${url}失败，错误信息: ${err.message}`,
      });
    }
  }
}
