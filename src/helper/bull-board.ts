/**
 *  bullmq board 默认使用内存管理当前已注册队列，在多进程、多机器情况下无法共享。因此使用redis存储已注册队列，并定时同步到bullmq board
 */
import { FORMAT, Inject } from '@midwayjs/core';
import { Job, IJob } from '@midwayjs/cron';
import { Redis } from '@midwayjs/redis';
import * as bullBoard from '@midwayjs/bull-board';
import * as bullmq from '@midwayjs/bullmq';

import { BullMQAdapter } from '@bull-board/api/bullMQAdapter';
import { getPrefix } from '@/utils/bull-mq';

/**
 * 使用 Redis Set 存储已注册队列
 */
export const BULL_BOARD_REGISTER_JOBS = 'BULL_BOARD_REGISTER_JOBS';

@Job({
  cronTime: FORMAT.CRONTAB.EVERY_PER_5_SECOND,
  start: true
})
export class BullBoardRegisterJob implements IJob {
  @Inject('redis')
  redis: Redis;

  @Inject()
  bullBoardManager: bullBoard.BullBoardManager;


  @Inject()
  bullmqFramework: bullmq.Framework;

  /**
   * 任务触发时，自动调用
   */
  async onTick() {

    if (!this.bullBoardManager || !this.bullBoardManager.getBullBoardOrigin()) return;

    const queueNames = await this.redis.smembers(BULL_BOARD_REGISTER_JOBS) || [];
    const queues = [];
    for (const queueName of queueNames) {
      const queue = this.bullmqFramework.getQueue(queueName) || this.bullmqFramework.createQueue(queueName, {
        prefix: getPrefix(queueName),
      });
      // @ts-ignore
      queues.push(new BullMQAdapter(queue));
    }
    this.bullBoardManager.replaceQueues(queues);
  }
}
