-- 令牌桶的key
local bucketKey = KEYS[1]
-- 令牌桶的容量
local capacity = tonumber(ARGV[1])
-- 每秒填充令牌桶的速率
local rate = tonumber(ARGV[2])
-- 当前时间戳（毫秒）
local now = tonumber(ARGV[3])
-- 请求的令牌数量
local requested = tonumber(ARGV[4])


-- 获取桶中的当前状态（当前令牌数和上次令牌填充时间）
local bucket = redis.call('hmget', bucketKey, 'tokens', 'lastRefillTime')
local tokens = tonumber(bucket[1])
local lastRefillTime = tonumber(bucket[2])

-- 如果桶是空的,则需要初始化
if tokens == nil then
  tokens = capacity
  lastRefillTime = now
end

-- 计算当前时间和上次填充时间的时间差，并更新令牌数量
local elapsedTime = math.max(now - lastRefillTime, 0)
-- 填充令牌，但是不能超过桶的容量
local filledTokens = math.min(capacity, tokens + math.floor(elapsedTime * rate / 1000))
local nextRefillTime = now

-- 判断桶内是否有足够的令牌
if filledTokens < requested then
  -- 返回需要等待的时间,单位毫秒
  local waitTime = math.min(math.ceil((requested - filledTokens) / rate * 1000), 1000)
  -- 保留桶中现有令牌数，但是更新最后填充时间
  redis.call('hmset', bucketKey, 'tokens', filledTokens, 'lastRefillTime', nextRefillTime)
  -- 设置过期时间
  redis.call('expire', bucketKey, math.ceil((capacity - filledTokens) / rate) + 1)
  -- 桶内没有足够的令牌，返回需要等待的时间
  return { 0, waitTime }
else 
  -- 桶内有足够的令牌，返回1
  tokens = filledTokens - requested
  lastRefillTime = now
  -- 更新桶中的令牌数和最后填充时间
  redis.call('hmset', bucketKey, 'tokens', tokens, 'lastRefillTime', lastRefillTime)
  -- 设置过期时间
  redis.call('expire', bucketKey, math.ceil((capacity - tokens) / rate) + 1)
  return { 1, 0 }
end


