import { Provide, Inject } from '@midwayjs/core';

import { LogService } from '@/helper/log';
import { PROCESS } from '@/constants';

@Provide()
export class OssService {
  @Inject()
  ossClient;

  @Inject()
  logger: LogService;

  /**
   * 上传文件到OSS
   * @param content
   */
  async put(content: string | Record<string, any>, path: string) {
    try {
      if (typeof content !== 'string') {
        content = JSON.stringify(content);
      }
      const resp = await this.ossClient.put(path, Buffer.from(content));
      const { name, url, res } = resp;
      const { size, rt } = res;
      this.logger.info({
        process: PROCESS.OSS_PUT,
        path,
        name,
        url,
        size,
        rt,
      });
      return resp;
    } catch (err) {
      this.logger.error({
        process: PROCESS.OSS_PUT,
        path,
        error: err.message,
      });
      throw new Error(`Put file to oss failed: ${err.message}`);
    }
  }

  /**
   * 从 OSS 获取文件
   */
  async get(name) {
    const result = await this.ossClient.get(name);
    return result.content.toString();
  }

  /**
   * 从 OSS 获取文件流
   */
  async getStream(name) {
    const { stream } = await this.ossClient.getStream(name);
    return stream;
  }

  /**
   * 扫描 OSS 下的文件
   */
  async scan(dir: string) {
    const result = await this.ossClient.list({
      prefix: dir,
    });
    return result.objects;
  }
}
