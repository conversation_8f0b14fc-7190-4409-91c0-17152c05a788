import { Provide, Inject, Scope, ScopeEnum } from '@midwayjs/core';
import * as bullmq from '@midwayjs/bullmq';

import { ConfigServerService, DefaultBullmqConfig } from '@/helper/config-server';
import { getPrefix } from '@/utils/bull-mq';

@Provide()
@Scope(ScopeEnum.Singleton)
export class WorkerPool {
  @Inject()
  configServerService: ConfigServerService;

  @Inject()
  bullmqFramework: bullmq.Framework;

  workerMap: Map<string, any> = new Map();

  async registerWorker(queueName: string, processor) {
    const config = await this.configServerService.getData();
    const workerConfig = config?.worker || DefaultBullmqConfig.worker;

    if (this.workerMap.has(queueName)) {
      const worker = this.workerMap.get(queueName);
      // 动态调整 worker 并发度
      if (worker.concurrency !== workerConfig.concurrency) {
        worker.concurrency = workerConfig.concurrency;
      }

      return worker;
    }

    const worker = this.bullmqFramework.createWorker(queueName, processor, {
      /**
       * 注意：
       *  - worker并发 依赖异步执行操作，类比于node原生的并发操作
       *  - limiter 全局适用。
       */
      limiter: {
        max: workerConfig.concurrency,
        duration: workerConfig.duration
      },
      prefix: getPrefix(queueName),
    });

    this.workerMap.set(queueName, worker);

    return worker;
  }
}