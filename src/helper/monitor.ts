/**
 * 指标采集，用于监控系统性能
 * 包括 当前进行中任务数、内存占用、CPU占用、网络占用等
 * 未来如果切换至其他监控系统（Porm），只需修改此文件即可
 */

import { Provide, ScopeEnum, Scope } from '@midwayjs/core';

import { collectDefaultMetrics, register, Histogram } from 'prom-client';

@Provide()
@Scope(ScopeEnum.Singleton)
export class MonitorService {

  /**
   * 成功更新OTS数据量,区分表维度
   */
  otsSuccessUpdateCount: Histogram = new Histogram({
    name: 'ots_success_update',
    help: 'OTS成功更新数据量',
    labelNames: ['table_name'],
    buckets: [1, 10, 50, 100, 500, 1000, 5000, 10000, 50000, 100000]
  });

  /**
   * 失败更新OTS数据量,区分表维度
   */
  otsFailUpdateCount: Histogram = new Histogram({
    name: 'ots_fail_update',
    help: 'OTS失败更新数据量',
    labelNames: ['table_name'],
    buckets: [1, 10, 50, 100, 500, 1000, 5000, 10000, 50000, 100000]
  });

  /**
   * 记录OTS批量更新数据量
   */
  async otsUpdateRecord(tableName: string, successCount: number, failCount: number) {
    this.otsSuccessUpdateCount.labels(tableName).observe(successCount);
    this.otsFailUpdateCount.labels(tableName).observe(failCount);
  }

  /**
   * 启动时采集系统指标
   */
  async bootstrap() {
    // 注册默认指标
    collectDefaultMetrics();
  }

  /**
   * 获取当前系统指标
   * @returns
   */
  async getMetrics() {
    return register.metrics();
  }
}
