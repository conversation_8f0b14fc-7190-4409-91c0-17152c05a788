import { Provide, Logger, Inject } from '@midwayjs/core';
import { ILogger } from '@midwayjs/logger';

import { getEnv } from '@ali/env-util';
import { format } from 'util';
import { isFunction } from 'lodash';

import { PROCESS } from '@/constants';
import { tracer } from '@/utils/tracer';

// 用于日志分割相关的不可见字符
const SEPARATOR = '\x01'; // 分隔符
const QUOTATION = '\x02'; // 引用符

@Provide()
export class LogService {
  @Inject()
  ctx;

  @Logger()
  logger: ILogger;

  /**
   * info级别日志
   */
  info(log: BizLog) {
    const useBizLogger =
      this &&
      this?.ctx &&
      this?.ctx?.getLogger &&
      isFunction(this.ctx.getLogger);
    if (useBizLogger) {
      this.ctx
        .getLogger('mainProcessLogger')
        .info(this.generateLogContent(log));
    } else {
      console.log(this.generateLogContent(log));
    }
  }

  /**
   * warn级别日志
   */
  warn(log: BizLog) {
    const useBizLogger =
      this &&
      this?.ctx &&
      this?.ctx?.getLogger &&
      isFunction(this.ctx.getLogger);
    if (useBizLogger) {
      this.ctx
        .getLogger('mainProcessLogger')
        .warn(this.generateLogContent(log));
    } else {
      console.warn(this.generateLogContent(log));
    }
  }

  /**
   * error级别日志
   */
  error(log: BizLog) {
    const useBizLogger =
      this &&
      this?.ctx &&
      this?.ctx?.getLogger &&
      isFunction(this.ctx.getLogger);
    if (useBizLogger) {
      this.ctx
        .getLogger('mainProcessLogger')
        .error(this.generateLogContent(log));
    } else {
      console.error(this.generateLogContent(log));
    }
  }

  /**
   * 主流程相关的业务日志，输出至 main-process.log
   * @param bizLog 业务日志
   * @returns
   */
  private generateLogContent(bizLog: BizLog) {
    const requestBody = tracer.get('x-body') || {};
    const { traceId, uuid, vendorCode, vendorType, rpcId } = requestBody;
    const { process, ...restBizLog } = bizLog;
    const infoList = [
      getEnv(),
      vendorType,
      vendorCode,
      traceId,
      rpcId,
      uuid,
      bizLog.process,
      `${QUOTATION}${format('%j', restBizLog)}${QUOTATION}`,
    ];
    try {
      const logContent = infoList.join(SEPARATOR);
      return logContent;
    } catch (ex) {
      this.error({
        process,
        message: ex.message,
      });
    }
  }

  /**
   * 数据变更相关的业务日志，输出至 row-history.log
   * @param log
   */
  sendRowHistoryLog(log: RowHistoryLog) {
    const rowHistoryLogger = this.ctx.getLogger('rowHistoryLogger');
    const requestBody = tracer.get('x-body') || {};
    const { traceId, uuid, vendorCode, vendorType, rpcId } = requestBody;
    const {
      isUpdated,
      tableName,
      primaryKey,
      attributes,
      level = 'INFO',
    } = log;
    const infoList = [
      getEnv(),
      vendorType,
      vendorCode,
      traceId,
      rpcId,
      uuid,
      tableName,
      isUpdated,
      `${QUOTATION}${format('%j', primaryKey)}${QUOTATION}`,
      attributes
        ? `${QUOTATION}${format('%j', attributes)}${QUOTATION}`
        : 'NOC', // NOC stand for NO CHANGE
    ];
    try {
      const logContent = infoList.join(SEPARATOR);

      if (level === 'ERROR') {
        rowHistoryLogger.error(logContent);
      } else {
        rowHistoryLogger.info(logContent);
      }
    } catch (ex) {
      console.error('上报字段变更日志失败:', ex);
    }
  }
}

interface RowHistoryLog {
  isUpdated: boolean;
  tableName: string;
  primaryKey: Record<string, any>;
  attributes?: Record<string, any>;
  level?: 'INFO' | 'ERROR';
}

interface BizLog {
  /**
   * 处理阶段
   */
  process: PROCESS;
  [key: string]: any;
}
