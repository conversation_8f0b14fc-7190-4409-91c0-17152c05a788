/**
 * Bullmq 配置,主要用于 Queue、Worker、限流器的配置
 */
import { Provide, Inject, Scope, ScopeEnum, DataListener } from '@midwayjs/core';
import { DiamondService } from '@ali/midway-base-middleware';

@Provide()
@Scope(ScopeEnum.Singleton)
export class ConfigServerService extends DataListener<BullmqConfig> {
  @Inject()
  diamondService: DiamondService;

  private dataId = 'switch-database-bullmq-config';

  private groupId = 'switch-database';

  /**
   * 初始化配置，单例模式，全局只调用一次
   */
  async initData() {
    const config = await this.diamondService.getConfig(this.dataId, this.groupId);

    try {
      return JSON.parse(config) || DefaultBullmqConfig;
    } catch (err) {
      console.error(`[Init] Parse bullmq config error: ${err.message}. Diamond config: ${config}`)
      return DefaultBullmqConfig;
    }
  }

  /**
   * 数据更新
   * @param callback 
   */
  onData(setData: (data: BullmqConfig) => void) {
    this.diamondService.subscribe({
      dataId: this.dataId,
      group: this.groupId
    }, (content) => {
      try {
        const config = JSON.parse(content) || {};
        setData(config);
      } catch (err) {
        console.error( `[Update] Parse bullmq config error: ${err.message} . Diamond config: ${content}`)
      }
    });
  }
}

/**
 * Diamond 配置结构
 */
export interface BullmqConfig {
  worker: {
    /**
     * 并发度
     */
    concurrency: number;

    /**
     * 等待时间
     */
    duration: number;
  },
  queue: {
    /**
     * 保留失败任务数量
     */
    removeOnFail: number;

    /**
     * 保留完成任务数量
     */
    removeOnComplete: number;
  },
  otsLimiter: {
    /**
     * 写入速率限制 /s
     */
    rate: number;

    /**
     * 令牌桶容量 
     */
    capacity: number;

    /**
     * 是否启用限流
     */
    enable?: boolean;

    /**
     * 限流作用表名
     */
    tableNames?: string[];
  }
}


export const DefaultBullmqConfig: BullmqConfig = {
  worker: {
    concurrency: 100,
    duration: 1000
  },
  queue: {
    removeOnFail: 10,
    removeOnComplete: 3
  },
  otsLimiter: {
    rate: 100,
    capacity: 100,
    enable: true,
    //配合 fliggy-switch-starrt 实时同步策略，默认仅对 ari_rate、feed_rate_plan 表进行限流
    tableNames: ['ari_rate','feed_rate_plan']
  }
}