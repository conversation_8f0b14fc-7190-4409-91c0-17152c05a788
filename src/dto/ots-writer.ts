import { ApiProperty } from '@midwayjs/swagger';

import { CommonParamsDTO } from '@/dto/common';
import { otsUpdateData } from '@/dto/example/ots-update-data';

/**
 * @description OTS 批量更新 DTO
 */
export class OTSBatchUpdateDTO extends CommonParamsDTO {
  @ApiProperty({
    description: '更新数据',
    required: true,
    isArray: true, // 指示这是一个数组
    example: otsUpdateData.updates,
  })
  updates: UpdateEntityDTO[];
}

export class DirectBatchUpdateDTO extends CommonParamsDTO {
  @ApiProperty({
    description: '数据',
    required: true,
    example: {
      type: 'oss',
      data: "database/travelbullz/prod/feed_country/020eae3a-3725-465d-9ee6-dcd2b13c8f4f_ac8a11e9-e690-402b-a9ef-2dbe0e96752c"
    }
  })
  data: Record<string, any>;
}



export class UpdateEntityDTO {
  @ApiProperty({
    description: '表名',
    example: 'feed_rate_plan',
    required: true,
  })
  tableName: string;

  @ApiProperty({
    description: '更新数据',
    required: true,
    example: {},
  })
  data: any;

  @ApiProperty({
    description: '是否默认增加time时间戳字段',
    required: false,
    example: false,
  })
  enableDefaultTime: boolean;
  
  @ApiProperty({
    // 历史逻辑兼容，目前只在expedia的feed_hotel_comment这张表的更新中有用到
    description: '强制指定某些字段为DOUBLE类型', 
    required: false,
    example: ["rating"],
  })
  forceDoubleTypeConfig: string[];

  @ApiProperty({
    description: '是否强制更新',
    required: false,
    example: true,
  })
  forceUpdate: boolean;
}
