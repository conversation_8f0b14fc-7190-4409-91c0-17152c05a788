import { ApiProperty } from '@midwayjs/swagger';

import { otsUpdateData } from '@/dto/example/ots-update-data';
export class CommonParamsDTO {
  @ApiProperty({
    description: '鹰眼链路ID',
    example: otsUpdateData.traceId,
    type: 'string',
  })
  traceId: string;

  @ApiProperty({
    description: 'UUID',
    example: otsUpdateData.uuid,
    type: 'string',
    required: false,
  })
  uuid: string;

  @ApiProperty({
    description: 'RPC ID',
    example: otsUpdateData.rpcId,
    type: 'string',
    required: false,
  })
  rpcId: string;

  @ApiProperty({
    description: '供应商Type',
    example: otsUpdateData.vendorType,
    type: 'string',
  })
  vendorType: string;

  @ApiProperty({
    description: '供应商Code',
    example: otsUpdateData.vendorCode,
    type: 'string',
  })
  vendorCode: string;

  @ApiProperty({
    description: '回调地址',
    required: false,
    example: 'http://127.0.0.1:7001/ots-writer/callback',
  })
  callbackUrl?: string;
}
