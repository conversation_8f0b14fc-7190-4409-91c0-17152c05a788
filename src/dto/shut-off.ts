import { ApiProperty } from '@midwayjs/swagger';

import { CommonParamsDTO } from '@/dto/common';

/**
 * 下架vendor table DTO
 */
export class ShutOffVendorTableDTO extends CommonParamsDTO {
  @ApiProperty({
    description: 'vendor code ',
    required: true,
    example: 'EPS',
  })
  vendorCode: string;

  @ApiProperty({
    description: '表名',
    required: true,
    example: 'feed_hotel_comment',
  })
  tableName: string;

  @ApiProperty({
    description: '时间戳，单位秒',
    required: true,
    example: 1748351100,
  })
  time: number;
}
