import { MidwayAppInfo, MidwayConfig } from '@midwayjs/core';

export default (appInfo: MidwayAppInfo) => {
  return {
    keys: appInfo.name + '_1683189267550_1127',
    redis: {
      client: {
        host: 'r-8vbf30459cbbb214.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: '4rxPBVi32eGZWTtq',
        db: 0
      }
    },
    bullmq: {
      defaultConnection: {
        host: 'r-8vbf30459cbbb214.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: '4rxPBVi32eGZWTtq',
        db: 0
      },
      defaultQueueOptions: {
        defaultJobOptions: {
          removeOnComplete: 30,  
          removeOnFail: 100,     
        }
      },
      contextLoggerFormat: info => {
        const { jobId, from } = info.ctx;
        return `${info.timestamp} ${info.LEVEL} ${info.pid} [${jobId} ${from.name}] ${info.message}`;
      },
    }
  } as MidwayConfig;
};
