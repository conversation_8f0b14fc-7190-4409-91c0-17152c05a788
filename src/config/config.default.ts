import { MidwayConfig, MidwayAppInfo } from '@midwayjs/core';

const SEPARATOR = '\x01'; // 分隔符

export default (appInfo: MidwayAppInfo) => {
  return {
    // use for cookie sign key, should change to your own and keep security
    keys: appInfo.name + '_1683189267550_1127',
    egg: {
      port: 7001,
    },
    bodyParser: {
      jsonLimit: '100mb',
    },
    security: { csrf: { enable: false } },
    keycenter: {
      appNum: "b2c23b6015074f798f327c6cf603cc75",
    },
    /**
     * IMPORTANT: 此 bucket 不会永久存储，仅保留3d. 
     */
    oss: {
      endpoint: 'oss-cn-zhangjiakou-internal.aliyuncs.com',
      accessKeyId: 'LTAI5t8LRGYrKJu7uXAHGrs6',
      accessKeySecret: '******************************',
      bucket: 'switch-database',
    },
    ots: {
      instanceName: 'fliggy-switch-p',
      accessKeyId: 'LTAI5tP4sbdV1m9vJUeaX9EW',
      accessKeySecret: '******************************',
      maxRetries: 20
    },
    sls: { // 通过阿里云 SDK 方式上报日志的初始化配置
      accessKeyId: 'LTAI5tARL7cXxF18n33Tmv31',
      secretAccessKey: '******************************',
      endpoint: 'http://cn-zhangjiakou-2-share.log.aliyuncs.com',
    },
    midwayLogger: {
      default: {
        transports: {
          file: {
            maxFiles: '3d',
          },
          error: {
            maxFiles: '3d',
          },
        }
      },
      clients: {
        mainProcessLogger: {
          fileLogName: 'main-process.log',
          format: log => {
            return `${log.timestamp}${SEPARATOR}${log.LEVEL}${SEPARATOR}${log.pid}${SEPARATOR}${log.message}`
          },
          transports: {
            file: {
              maxFiles: '3d',
            },
            error: {
              maxFiles: '3d',
            },
          }
        },
        rowHistoryLogger: {
          fileLogName: 'row-history.log',
          format: log => {
            return `${log.timestamp}${SEPARATOR}${log.LEVEL}${SEPARATOR}${log.pid}${SEPARATOR}${log.message}`
          },
          transports: {
            file: {
              maxFiles: '3d',
            },
            error: {
              maxFiles: '3d',
            },
          },
          enableConsole: false,
        },
        bullMQLogger: {
          fileLogName: 'midway-bullmq.log',
        },
        // 本地任务仅输出warn级别以上日志
        cronLogger: {
          level:'warn',
        }
      }
    },
    bullBoard: {
      basePath: '/bull-ui',
    },
    redis: {
      client: {
        host: 'r-8vbd0e0ba5950154.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: 'jQ4OLa2DkSYI0ge1oVh3',
        db: 0
      }
    },
    bullmq: {
      defaultConnection: {
        host: 'r-8vbd0e0ba5950154.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: 'jQ4OLa2DkSYI0ge1oVh3',
        db: 0
      },
      defaultQueueOptions: {
        defaultJobOptions: {
          removeOnComplete: 3,  // 成功后只保留最近 3 条记录
          removeOnFail: 10,     // 失败后只保留最近 10 条记录
        }
      },
      contextLoggerFormat: info => {
        const { jobId, from } = info.ctx;
        return `${info.timestamp} ${info.LEVEL} ${info.pid} [${jobId} ${from.name}] ${info.message}`;
      },
    }
  } as MidwayConfig;
};
