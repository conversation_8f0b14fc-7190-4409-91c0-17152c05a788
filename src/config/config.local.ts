import { MidwayAppInfo, MidwayConfig } from '@midwayjs/core';

export default (appInfo: MidwayAppInfo) => {
  return {
    keys: appInfo.name + '_1683189267550_1127',
    security: {
      csrf: false,
    },
    keycenter: {
      appNum: 'b237375effd34d9c80f6cf0631f1b29c'
    },
    oss: {
      // 本地调试时，使用外网地址。否则网络隔离连不上
      endpoint: 'oss-cn-zhangjiakou.aliyuncs.com',
      accessKeyId: 'LTAI5t8LRGYrKJu7uXAHGrs6',
      accessKeySecret: '******************************',
      bucket: 'switch-database',
    },
    ots: {
      instanceName: 'fliggy-switch',
      accessKeyId: 'LTAI5tP4sbdV1m9vJUeaX9EW',
      accessKeySecret: 'BMCOlJ1nMHGrAH4sbRB0IlSKcFD1J<PERSON>',
      maxRetries: 20
    },
    redis: {
      client: {
        host: 'r-8vb8be6f612b8a14.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: '95cXmBPgyoHqepYj',
        db: 0
      }
    },
    bullmq: {
      defaultConnection: {
        host: 'r-8vb8be6f612b8a14.redis.zhangbei.rds.aliyuncs.com',
        port: 6379,
        password: '95cXmBPgyoHqepYj',
        db: 0
      },
      defaultQueueOptions: {
        defaultJobOptions: {
          // 本地调试阶段，留的大一点方便排查
          removeOnComplete: 3000,  
          removeOnFail: 10000,     
        }
      },
      contextLoggerFormat: info => {
        const { jobId, from } = info.ctx;
        return `${info.timestamp} ${info.LEVEL} ${info.pid} [${jobId} ${from.name}] ${info.message}`;
      },
    }
  } as MidwayConfig;
};
