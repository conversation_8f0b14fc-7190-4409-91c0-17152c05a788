/**
 * ClsTracingMiddleware 是一个用于追踪上下文数据的中间件。
 * 它使用 tracer 工具来捕获和存储请求体、请求头和查询参数。
 * 通过这种方式，我们可以在整个请求生命周期中访问这些数据。
 * !!不适用于 队列 任务!!
 */
import { Middleware, IMiddleware } from '@midwayjs/core';
import { NextFunction, Context } from '@midwayjs/web';

import { pick } from 'lodash';

import { tracer } from '@/utils/tracer';

@Middleware()
export class ClsTracingMiddleware implements IMiddleware<Context, NextFunction> {
  resolve() {
    return async (ctx: Context, next: NextFunction) => {
      return tracer.runPromise(async () => {
        const body = pick(ctx.request.body, ['traceId', 'uuid', 'vendorCode', 'vendorType', 'rpcId']);
        const headers = ctx.headers;
        const query = ctx.query;

        tracer.set('x-body', body);
        tracer.set('x-headers', headers);
        tracer.set('x-query', query);
        
        return await next();
      });
    };
  }
}