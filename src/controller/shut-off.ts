import { Controller, Inject, Post, Body } from '@midwayjs/core';
import { ApiOperation } from '@midwayjs/swagger';

import { ShutOffVendorTableDTO } from '@/dto/shut-off';
import { ShutOffService } from '@/service/shut-off';

@Controller('/shut-off')
export class ShutOffController {
  @Inject()
  shutOffService: ShutOffService;

  /**
   * 下架
   */
  @Post('/vendor-table')
  @ApiOperation({
    summary: '下架特定vendor下的特定table数据'
  })
  async shutoffVendorTable(@Body() body: ShutOffVendorTableDTO) { 
    return await this.shutOffService.shutOff(body);
  }
}
