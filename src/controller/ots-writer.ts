import { Controller, Inject, Post, Body } from '@midwayjs/core';
import { ApiOperation } from '@midwayjs/swagger';

import { BatchUpdateQueueService } from '@/service/queue/batch-update';
import { OTSBatchUpdateDTO } from '@/dto/ots-writer';
import { BatchUpdateWorker } from '@/worker/batch-update-ots';
import { DirectBatchUpdateDTO } from '@/dto/ots-writer';

@Controller('/ots-writer')
export class OtsWriterController {

  @Inject()
  batchUpdateQueueService: BatchUpdateQueueService;

  @Inject()
  batchUpdateWorker: BatchUpdateWorker;

  @Post('/batch-write')
  @ApiOperation({
    summary: 'OTS批量更新'
  })
  async batchWrite(@Body() body: OTSBatchUpdateDTO) {
    return await this.batchUpdateQueueService.update(body);
  }


  @Post('/batch-write/debug')
  @ApiOperation({
    summary: '调试OTS批量更新(非队列)'
  })
  async debugBatchWrite(@Body() body: DirectBatchUpdateDTO) {
    return await this.batchUpdateWorker.process(body);
  }
}