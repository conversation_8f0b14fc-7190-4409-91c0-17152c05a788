import { Inject, Provide } from '@midwayjs/core';
import { HttpService } from '@midwayjs/axios';

import { OtsService } from '@/service/ots';
import { formatRows, getTablePrimaryKeys } from '@/utils/ots';
import { OTSBatchUpdateDTO } from '@/dto/ots-writer';
import { PROCESS } from '@/constants';
import { OssService } from '@/helper/oss';
import { MonitorService } from '@/helper/monitor';
import { tracingRun } from '@/utils/tracer';
import { LogService } from '@/helper/log';
import { CallbackService } from '@/helper/callback';
import { OtsValidatorIntegration } from '@/validator/validator-integration';

@Provide()
export class BatchUpdateWorker {
  @Inject()
  otsService: OtsService;

  @Inject()
  ossService: OssService;

  @Inject()
  monitor: MonitorService;

  @Inject()
  httpService: HttpService;

  @Inject()
  callbackService: CallbackService;

  @Inject()
  otsValidatorIntegration: OtsValidatorIntegration;

  @Inject()
  logger: LogService;

  async process(job): Promise<void> {
    let { data } = job.data;

    const resp = await this.ossService.get(data);
    data = JSON.parse(resp);

    const { updates = [], ...rest } = data as OTSBatchUpdateDTO;

    const traceInfo = {
      body: rest,
    };

    return tracingRun(async () => {
      /**
       * 作用：串行写入多表，通过遍历 + await batchUpdate 实现
       * 应用场景：常用于 写入完成后同步更新依赖表的场景，如：先写入 ari_daily_rate，再写入 ari_rate
       */
      for (let i = 0; i < updates.length; i++) {
        const update = updates[i];

        const {
          tableName,
          forceUpdate,
          enableDefaultTime = true,
          forceDoubleTypeConfig,
        } = update;

        // 待更新数据
        const needUpdateData = update.data;

        // 数据校验
        const validationResult =
          await this.otsValidatorIntegration.validateOtsData(
            tableName,
            needUpdateData
          );
        if (validationResult.action === 'abort') {
          this.logger.error({
            process: PROCESS.OTS_VALIDATE,
            tableName,
            message:
              '数据校验失败，中止写入: ' + JSON.stringify(validationResult),
          });
          continue;
        }

        const data = validationResult.data;

        const primaryKeys = getTablePrimaryKeys(tableName);

        const rows = data
          .map(item => {
            return (
              item &&
              formatRows(
                item,
                primaryKeys,
                enableDefaultTime,
                forceDoubleTypeConfig
              )
            );
          })
          .filter(Boolean) || [
          formatRows(
            data,
            primaryKeys,
            enableDefaultTime,
            forceDoubleTypeConfig
          ),
        ];

        try {
          const { success, fail, notUpdate, failReason } =
            await this.otsService.batchUpdate({
              tableName: tableName,
              rows,
              forceUpdate,
            });
          
          // 回调用户告知写入完成
          await this.callbackService.dispatch(
            rest.callbackUrl,
            {
              success,
              fail,
              notUpdate,
              failReason,
            },
            {
              jobName: 'batch-update',
            }
          );
        } catch (err) {
          this.logger.error({
            process: PROCESS.OTS_BATCH_UPDATE,
            errorMessage: err.message,
          });
        }
      }
    }, traceInfo);
  }
}
