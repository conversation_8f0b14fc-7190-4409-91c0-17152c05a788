import { Inject, Provide } from '@midwayjs/core';

import { transform } from 'stream-transform';
import { parse } from 'csv-parse';
import { createHash } from 'crypto';
import * as TableStore from 'tablestore';

import { OssService } from '@/helper/oss';
import { formatRows } from '@/utils/ots';

@Provide()
export class OtsSyncWorker {
  @Inject()
  ossService: OssService;

  @Inject('otsClient')
  otsClient: TableStore.Client;
  //  排除不需要同步的表
  syncTableExcludes = ['ari_daily_rate'];
  //  只同步特定的 vendor
  syncVendorIncludes = ['intl_GMT', 'intl_GMTJA'];

  /**
   * 同步数据至 OTS
   * @param job
   */
  async process(job) {
    const { file } = job.data;
    const { name } = file;
    // 下载 OSS 文件
    const stream = await this.ossService.getStream(name);

    // 解析 csv 文件
    const csvParser = parse({
      columns: true,
      trim: true,
      skip_empty_lines: true,
      delimiter: ',',
      relax_column_count: true,
      bom: true,
      skip_records_with_error: true,
      raw: false,
    });
    csvParser.on('error', err => {
      csvParser.resume();
    });

    let rows = [];

    // Transform stream ，batchUpdate Size 100
    const transformStream = transform((record, callback) => {
      try {
        callback(null, record);
      } catch (err) {
        console.error('Transform stream error: ', err);
        // 出错时，让流停止
        callback(null, null);
      }
    });

    transformStream.on('data', async data => {
      const { __time__, vendorCode, primaryKey, tableName } = data;
      let primaryKeyObj = JSON.parse(primaryKey);
      const requestId = this._generateUuid(primaryKey);
      const next = {
        time: Number(__time__),
        vendorCode,
        tableName,
        requestId,
        rowMeta: JSON.stringify(primaryKeyObj),
      };
      // 只同步特定场景，避免资源浪费
      if (!this.syncTableExcludes.includes(tableName) && this.syncVendorIncludes.includes(vendorCode) ) {
        rows.push(next);
      }

      const rowHistoryPrimaryKeys = ['vendorCode', 'tableName', 'requestId'];

      while (rows.length >= 100) {
        transformStream.pause();
        const batch = rows.splice(0, 100);
        const updateRows = batch
          .map(row => {
            return row && formatRows(row, rowHistoryPrimaryKeys, false);
          })
          .filter(Boolean) || [
          formatRows(batch[0], rowHistoryPrimaryKeys, false),
        ];

        const params = {
          tables: [
            {
              tableName: 'row_history_cache',
              rows: updateRows.map(row => {
                return {
                  type: 'UPDATE' as const,
                  // 忽略行是否存在，存在任一字段不相等则更新
                  condition: new TableStore.Condition(
                    TableStore.RowExistenceExpectation.IGNORE,
                    null
                  ),
                  primaryKey: this._formatPrimaryKey(row.primaryKey),
                  returnContent: {
                    returnType: TableStore.ReturnType.Primarykey,
                  },
                  attributeColumns: [
                    { PUT: this._formatColumns(row.attributes) },
                  ],
                };
              }),
            },
          ],
        };

        const { tables } = await this.otsClient.batchWriteRow(params);

        tables.forEach((row, index) => {
          if (!row.isOk) {
            console.error('Batch write row error: ', row.errorMessage);
          }
        });

        // 写入 OTS
        transformStream.resume();
      }
    });

    stream
      .pipe(csvParser)
      .pipe(transformStream)
      .on('error', err => {
        console.error('Pipe stream error: ', err);
      });
  }

  private _generateUuid(str: string) {
    return createHash('sha256').update(str).digest('hex');
  }

  /**
   * 格式化 OTS 主键
   * @param primaryKey OTS 主键
   * @returns 格式化后的主键
   */
  private _formatPrimaryKey(primaryKey: any[]) {
    primaryKey = primaryKey?.map((item: any) => {
      const name = item.name;
      const value = item.value;
      item = { [name]: value };
      return item;
    });
    return primaryKey;
  }

  /**
   * 格式化 OTS 列数据
   * @param columns OTS 列数据
   * @returns 格式化后的列数据
   */
  private _formatColumns(columns: { [key: string]: any }[]) {
    return columns?.map((item: any) => {
      Object.keys(item).forEach(key => {
        if (item[key] == null) {
          throw new Error('column value is null : ' + key);
        }
      });
      return item;
    });
  }
}
