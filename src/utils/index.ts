import { getEnv } from '@ali/env-util';

export const env = getEnv();

/**
 * 将持续时间转为更加易读的格式
 * @param time 持续时间，单位秒
 */
export const formatUpTime = (time: number) => { 
  const days = Math.floor(time / 86400);
  const hours = Math.floor((time % 86400) / 3600);
  const minutes = Math.floor((time % 3600) / 60);
  const seconds = Math.floor(time % 60);

  return (
    (days ? days + 'd ' : '') +
    (hours ? hours + 'h ' : '') +
    (minutes ? minutes + 'm ' : '') +
    (seconds ? seconds + 's' : '')
  );
}

/**
 * 将内存指标转换为更易读的格式
 */
export const formatMem = (mem: number) => {
  if (mem < 1024) {
    return `${mem}B`;
  } else if (mem < 1024 * 1024) {
    return `${(mem / 1024).toFixed(2)}KB`;
  } else if (mem < 1024 * 1024 * 1024) {
    return `${(mem / 1024 / 1024).toFixed(2)}MB`;
  } else {
    return `${(mem / 1024 / 1024 / 1024).toFixed(2)}GB`;
  }
}