import * as TableStore from 'tablestore';
import { getEnv } from '@ali/env-util';

import { TABLESTORE_ENUM, TABLE_STORE_NAME } from '@/constants';

const Long = TableStore.Long;

export const env = getEnv();

export const isTableStoreLongType = attr => {
  return (
    typeof attr === 'object' &&
    typeof attr?.toNumber === 'function' &&
    typeof attr?.toBuffer === 'function'
  );
};

export function formatRows(
  item,
  primaryKey,
  enableDefaultTime,
  forceDoubleTypeConfig?: string[]
) {
  const primaryKeys = [];
  primaryKey.forEach(key => {
    // 确保主键不为 undefined， null或空字符串
    // 此处不要抛出错误：抛出错误外层没有兜住的情况下OTS存失败会阻塞请求主流程
    // 只针对有值的主键做转换，当主键不够不足以匹配OTS表时，OTS存储过程会报错
    const type = Object.prototype.toString.call(item[key]).slice(8, -1);
    if (type !== 'Undefined' && type !== 'Null' && item[key] !== '') {
      primaryKeys.push({
        name: camelToUnderscore(key),
        value: item[key],
      });
    }
  });
  const attributeColumns = [] as any[];
  // 兜底 time 字段
  let hasTime = false;
  let hasEnv = false;

  Object.keys(item).forEach(key => {
    if (key === 'time') {
      // 判断属性中是否存在 time 字段
      hasTime = hasTime || true;
    }
    if (key === 'env') {
      hasEnv = hasEnv || true;
    }
    // 从属性中排除主键后存属性列，并且手动排除data(原始返回), request(原始入参)
    if (!primaryKey.concat(['data', 'request']).includes(key)) {
      const type = Object.prototype.toString.call(item[key]).slice(8, -1);
      if (type !== 'Undefined' && type !== 'Null') {
        attributeColumns.push({
          [camelToUnderscore(key)]: formatDataForOTS({
            key,
            value: item[key],
            forceDoubleTypeConfig,
          }),
        });
      }
    }
  });
  // 如果没有 time 字段并且 enableDefaultTime 开启，则手动添加
  if (!hasTime && enableDefaultTime) {
    attributeColumns.push({
      time: formatDataForOTS({
        key: 'time',
        value: Date.now(),
        forceDoubleTypeConfig,
      }),
    });
  }
  // 如果没有 env 字段，则手动添加
  if (!hasEnv) {
    attributeColumns.push({
      env: env,
    });
  }

  return {
    primaryKey: primaryKeys,
    attributes: attributeColumns,
  };
}

export function camelToUnderscore(camelCase: string): string {
  return camelCase.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
}

export function formatRemoteRowDataToMapping(row: TableStore.Row) {
  const { attributes = [], primaryKey = [] } = row;
  const ret = {} as Record<string, any>;
  primaryKey.forEach(pk => {
    ret[pk.name] = pk.value;
  });
  attributes.forEach(attr => {
    ret[attr.columnName] = attr.columnValue;
  });
  return ret;
}

export function formatPrimaryKeyList(pkList: Record<string, any>) {
  const ret = [];
  pkList.forEach(pk => {
    Object.entries(pk).forEach(([key, value]) => {
      if (value === TABLESTORE_ENUM.INF_MAX) {
        ret.push({ [key]: TableStore.INF_MAX });
      } else if (value === TABLESTORE_ENUM.INF_MIN) {
        ret.push({ [key]: TableStore.INF_MIN });
      } else {
        ret.push({ [key]: value });
      }
    });
  });
  return ret;
}

export function formatDataForOTS(column: {
  key: string;
  value: any;
  forceDoubleTypeConfig: string[];
}) {
  const { key, value: params, forceDoubleTypeConfig } = column;
  const type = Object.prototype.toString.call(params).slice(8, -1);
  switch (type) {
    case 'Object': {
      if (Buffer.isBuffer(params.buffer)) {
        return params;
      } else {
        return JSON.stringify(params);
      }
    }
    case 'Array': {
      return JSON.stringify(params);
    }
    case 'Number': {
      if (forceDoubleTypeConfig && forceDoubleTypeConfig.includes(key)) {
        return params;
      }
      return Long.fromNumber(params);
    }
    // string / boolean
    default:
      return params;
  }
}

export const TABLE_STORE_COLUMN = {
  ari_daily_rate: [
    'hotel_id',
    'inclusive_amount',
    'currency',
    'inventory',
    'rate_id',
    'rate_plan_id',
    'room_type_id',
    'status',
    'time',
  ],
  ari_rate: [
    'currency',
    'los',
    'rate_plan_id',
    'room_inventory',
    'room_type_id',
    'status',
    'name',
    'occupancy',
    'time',
    'language',
  ],
  feed_rate_plan: [
    'rate_plan_name',
    'payment_type',
    'price_type',
    'meal',
    'cancel_policies',
    'status',
    'language',
    'time',
  ],
  ari_room_inventory: [
    'vendor_type',
    'vendor_code',
    'blocked_count',
    'count',
    'disabled',
  ],
  feed_city: [
    'vendor_type',
    'city_name',
    'city_name_translated',
    'time',
    'language',
  ],
  feed_country: [
    'vendor_type',
    'country_name',
    'country_name_translated',
    'country_iso',
    'time',
    'language',
  ],
  feed_hotel: [
    'vendor_type',
    'hotel_name',
    'hotel_name_translated',
    'longitude',
    'latitude',
    'tel',
    'addresses',
    'pictures',
    'facilities',
    'time',
    'language',
  ],
  feed_room_type: [
    'vendor_type',
    'max_occupancy',
    'name',
    'name_translated',
    'size_of_room',
    'bed',
    'bed_type_desc',
  ],
};

export function getTablePrimaryKeys(tableName: string) {
  let primaryKeys = [];
  switch (tableName) {
    case TABLE_STORE_NAME.FEED_RATE_PLAN:
      primaryKeys = ['requestPk', 'vendorCode', 'hotelId', 'ratePlanId'];
      break;
    case TABLE_STORE_NAME.ARI_RATE:
      primaryKeys = ['requestPk', 'vendorCode', 'hotelId', 'rateKey'];
      break;
    case TABLE_STORE_NAME.ARI_DAILY_RATE:
      primaryKeys = ['requestPk', 'vendorCode', 'rateKey', 'date'];
      break;
    case TABLE_STORE_NAME.FEED_HOTEL:
      primaryKeys = ['requestPk', 'vendorCode', 'hotelId'];
      break;
    case TABLE_STORE_NAME.FEED_CITY:
      primaryKeys = ['requestPk', 'vendorCode', 'countryId', 'cityId'];
      break;
    case TABLE_STORE_NAME.FEED_COUNTRY:
      primaryKeys = ['requestPk', 'vendorCode', 'countryId'];
      break;
    case TABLE_STORE_NAME.FEED_HOTEL_COMMENT:
      primaryKeys = ['requestPk', 'vendorCode', 'hotelId', 'id'];
      break;
    case TABLE_STORE_NAME.FEED_INVOICE:
      primaryKeys = ['requestPk', 'vendorCode', 'invoiceRuleType', 'domainId'];
      break;
    case TABLE_STORE_NAME.FEED_PROPERTY:
      primaryKeys = ['requestPk', 'vendorCode', 'domain', 'id'];
      break;
    case TABLE_STORE_NAME.FEED_PROPERTY_RELATION:
      primaryKeys = ['id', 'domain'];
      break;
    case TABLE_STORE_NAME.FEED_ROOM_TYPE:
      primaryKeys = ['requestPk', 'vendorCode', 'hotelId', 'roomTypeId'];
      break;
    case TABLE_STORE_NAME.FEED_CACHE:
      primaryKeys = ['requestPk', 'vendorCode', 'domain', 'id'];
      break;
    case TABLE_STORE_NAME.ARI_ROOM_INVENTORY:
      primaryKeys = ['requestPk', 'hotelId', 'roomTypeId', 'date'];
      break;
    default:
      throw new Error(`未知的表名: ${tableName}`);
  }

  return primaryKeys;
}
