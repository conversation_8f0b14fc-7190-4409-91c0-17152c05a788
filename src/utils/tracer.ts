import { createNamespace } from 'cls-hooked';

import { getEnv } from '@ali/env-util';
import { format } from 'util';

export const tracer = createNamespace('switch-database');

/**
 * 自动携带追踪信息运行函数
 * @param fn 要执行的函数
 * @param traceInfo 追踪信息对象，包含 body, headers, query
 * @returns 函数执行结果
 */
export const tracingRun = (fn: Function, traceInfo) => {
  return tracer.run(async () => {
    // 将追踪信息存入上下文
    tracer.set('x-body', traceInfo.body || {});
    tracer.set('x-headers', traceInfo.headers || {});
    tracer.set('x-query', traceInfo.query || {});

    try {
      return await fn();
    } catch (err) {
      const { traceId, uuid, vendorCode, vendorType, rpcId } = traceInfo.body || {};
      const process = 'SWITCH_DATABASE_INNER_ERROR';
      const message = err.message;
      const bizLog = {
        message,
      }
      let logContent = `${getEnv()} ${vendorType} ${vendorCode} ${traceId} ${rpcId} ${uuid} ${process} `;
      try {
        logContent += format('%j', bizLog);
      } catch (ex) {
        logContent += `${ex.message}`;
      }
      console.error(logContent);
    }
  });
}

