{"name": "switch-database", "version": "1.0.0", "description": "switch对接数据中心", "private": true, "author": "钟吾", "license": "MIT", "repository": "**************************:tripnode/switch-database.git", "dependencies": {"@ali/env-util": "^1.13.0", "@ali/midway": "8.4.3", "@ali/midway-base-middleware": "^3", "@ali/midway-keycenter": "^3.2.0", "@ali/midway-otel": "3.2.1", "@ali/switch-core": "^1.3.1", "@ali/switch-hotel-utils": "^1.3.12", "@ali/switch-utils": "5.0.0", "@bull-board/api": "^6.7.1", "@midwayjs/axios": "^3.12.3", "@midwayjs/bootstrap": "3.14.4", "@midwayjs/bull-board": "^3.20.0", "@midwayjs/bullmq": "^3.20.0", "@midwayjs/core": "^3.14.4", "@midwayjs/cron": "^3.20.0", "@midwayjs/decorator": "3.14.4", "@midwayjs/logger": "latest", "@midwayjs/process-agent": "^3.15.6", "@midwayjs/redis": "^3.20.0", "@midwayjs/sequelize": "^3.16.6", "@midwayjs/swagger": "^3.13.7", "@midwayjs/validate": "^3", "@midwayjs/web": "3.14.7", "ali-oss": "^6.22.0", "aliyun-sdk": "^1.12.10", "bullmq": "^5.40.1", "cls-hooked": "^4.2.2", "dayjs": "^1.11.13", "egg": "^2.0.0", "fs-extra": "^11.1.1", "ioredis": "^5.4.2", "lodash": "^4.17.21", "pidusage": "^3.0.2", "prom-client": "^15.1.3", "sequelize": "^6.37.3", "sequelize-typescript": "^2.1.6", "csv-parse": "^5.5.6", "stream-transform": "^3.3.3", "tablestore": "^5.5.1", "uuid": "^9.0.1"}, "devDependencies": {"@ali/midway-cli-plugin-ali": "^4.0.0", "@ali/midway-scripts": "^9.0.0", "@midwayjs/cli": "^2.0.0", "@midwayjs/egg-ts-helper": "^1.0.1", "@midwayjs/mock": "^3.14.7", "@types/ali-oss": "^6.16.11", "@types/jest": "^29.2.0", "@types/lodash": "^4.17.13", "@types/node": "^20.3.1", "@types/tablestore": "^5.1.3", "cross-env": "^6.0.0", "egg-mock": "^3.26.0", "jest": "^29.2.2", "mwts": "^1.0.5", "mwtsc": "^1.10.0", "oxlint": "^0.2.6", "prettier": "^3.2.4", "swagger-ui-dist": "^5.17.14", "ts-jest": "^29.0.3", "typescript": "~4.8.0"}, "scripts": {"dev": "cross-env ets && cross-env NODE_ENV=local midway-bin dev --ts --debug ", "test": "midway-bin test --ts", "cov": "midway-bin cov --ts", "lint": "oxlint --fix", "format": "prettier --cache --write --list-different .", "ci": "npm run cov", "build": "midway-bin build -c"}, "midway-bin-clean": [".vscode/.tsbuildinfo", "dist"], "egg": {"framework": "@ali/midway"}, "midway-cli": {"plugins": ["@ali/midway-cli-plugin-ali"]}, "engines": {"node": ">=16.19.1"}, "config": {"build": {"deps": "isolation"}, "docker": {"os": 7}, "nginx": {"client_max_body_size": "100m"}}}