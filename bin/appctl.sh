#!/bin/bash

#######  error code specification  #########
# Please update this documentation if new error code is added.
# 1   => reserved for script error
# 2   => bad usage
# 3   => bad user
# 4   => tomcat start failed
# 5   => preload.sh check failed
# 6   => hsf online failed
# 7   => nginx start failed
# 8   => status.taobao check failed
# 9   => hsf offline failed
# 128 => exit with error message

PROG_NAME=$0
ACTION=$1
TOMCAT_PORT="7001"
usage() {
    echo "Usage: $PROG_NAME {start|stop|online|offline|pubstart|restart|deploy}"
    exit 2 # bad usage
}

if [ "$UID" -eq 0 ]; then
    echo "can't run as root, please use: sudo -u admin $0 $@"
    exit 3 # bad user
fi

if [ $# -lt 1 ]; then
    usage
    exit 2 # bad usage
fi

APP_HOME=$(cd $(dirname $0)/..; pwd)
source "$APP_HOME/bin/setenv.sh"
source "$APP_HOME/bin/hook.sh"

die() {
    if [ "$#" -gt 0 ]; then
        echo "ERROR:" "$@"
    fi
    exit 128
}

# fix "-Xmx", "-Xms", "-Xmn", delete cms options if need.
fix_jvm_opts() {
    local env_name="$1"
    local os_mem=$(grep -P '^MemTotal.*kB$' /proc/meminfo | grep -Po '\d+')
    if [ "x${os_mem}" = "x" ]; then
        echo "WARN: get 'MemTotal' failed, check jvm opts abort."
    else
        # convert to MB
        (( os_mem = os_mem / 1024 ))
        echo "INFO: MemTotal: $os_mem MB"
        local origin_opts=$(eval echo '$'${env_name})
        # NOTE: in order to test "$?", MUST split "local" statement and command
        local new_opts
        new_opts=$(/usr/bin/env python ${APP_HOME}/bin/fixjvmopts.py "${os_mem}" ${origin_opts})
        test "$?" -eq 0 || exit 1
        if [ "${origin_opts}" != "${new_opts}" ]; then
            eval "${env_name}=\"${new_opts}\""
        fi
    fi
    echo "USING JVM OPTS: \"${new_opts}\""
}

# check and clear $CATALINA_PID file.
# (copy from catalina.sh)
check_catalina_pid() {
  if [ ! -z "$CATALINA_PID" ]; then
    if [ -f "$CATALINA_PID" ]; then
      if [ -s "$CATALINA_PID" ]; then
        echo "Existing PID file found during start."
        if [ -r "$CATALINA_PID" ]; then
          PID=`cat "$CATALINA_PID"`
          ps -p $PID >/dev/null 2>&1
          if [ $? -eq 0 ] ; then
            echo "Tomcat appears to still be running with PID $PID. Start aborted."
            exit 1
          else
            echo "Removing/clearing stale PID file."
            rm -f "$CATALINA_PID" >/dev/null 2>&1
            if [ $? != 0 ]; then
              if [ -w "$CATALINA_PID" ]; then
                cat /dev/null > "$CATALINA_PID"
              else
                echo "Unable to remove or clear stale PID file. Start aborted."
                exit 1
              fi
            fi
          fi
        else
          echo "Unable to read PID file. Start aborted."
          exit 1
        fi
      else
        rm -f "$CATALINA_PID" >/dev/null 2>&1
        if [ $? != 0 ]; then
          if [ ! -w "$CATALINA_PID" ]; then
            echo "Unable to remove or write to empty PID file. Start aborted."
            exit 1
          fi
        fi
      fi
    fi
  fi
}

update_conf() {
    local PROGRAM="$1"
    local DEST="$2"
    if [ -d "$APP_HOME/conf/$PROGRAM" ]; then
        echo "update $PROGRAM conf..." >> "${CATALINA_OUT}"
        rsync -av --itemize-changes "$APP_HOME/conf/$PROGRAM/" "$DEST/" >> "${CATALINA_OUT}" || exit 1
    fi
}

prepare_catalina_base() {
    rm -rf "$CATALINA_BASE" || exit 1
    mkdir -p "$CATALINA_BASE" "$CATALINA_BASE/"{deploy,temp,work} || exit 1
    cp -rf "$CATALINA_HOME/conf" "$CATALINA_BASE/" || exit 1
    update_conf "tomcat" "$CATALINA_BASE/conf"
    sed -i -e "s/\"8080\"/\"$TOMCAT_PORT\"/g" $CATALINA_BASE/conf/server.xml
    #forece set /status.taobao to default servlet
    sed -i -e 's/<url-pattern>\/<\/url-pattern>/<url-pattern>\/<\/url-pattern>\n\t<url-pattern>\/status.taobao<\/url-pattern>/' $CATALINA_BASE/conf/web.xml
}

extract_tgz() {
    local tgz_path="$1"
    local dir_path="$2"
    local rm_nodejs_dir="$3"
    echo "extract ${tgz_path}"
    cd "${APP_HOME}/target" || exit 1
    rm -rf "${dir_path}" || exit 1
    if [ -d "$NODEJS_APP_ROOT" ] && [ -n "$rm_nodejs_dir" ]; then
        rm -rf "$NODEJS_APP_ROOT" || exit 1
    fi
    tar xzf "${tgz_path}" || exit 1
    test -d "${dir_path}" || die "no directory: ${dir_path}"
    touch --reference "${tgz_path}" "${tgz_path}.timestamp" || exit 1
}

: '
    # dir exists
        # tgz exists
            # tgz is new - extract_tgz
            # tgz is not new - return SUCCESS
        # tgz not exists - return SUCCESS
    # dir not exists
        # tgz exists - extract_tgz
        # tgz not exists - return FAIL
'
update_target() {
    local tgz_name="$1"
    local dir_name="$2"
    local rm_nodejs_dir="$3"

    local tgz_path="${APP_HOME}/target/${tgz_name}"
    local dir_path="${APP_HOME}/target/${dir_name}"

    local error=0
    # dir exists
    if [ -d "${dir_path}" ]; then
        # tgz exists
        if [ -f "${tgz_path}" ]; then
            local need_tar=0
            if [ ! -e "${tgz_path}.timestamp" ]; then
                need_tar=1
            else
                local tgz_time=$(stat -L -c "%Y" "${tgz_path}")
                local last_time=$(stat -L -c "%Y" "${tgz_path}.timestamp")
                if [ $tgz_time -gt $last_time ]; then
                    need_tar=1
                fi
            fi
            # tgz is new - extract_tgz
            if [ "${need_tar}" -eq 1 ]; then
                extract_tgz "${tgz_path}" "${dir_path}" "${rm_nodejs_dir}"
            fi
            # tgz is not new - return SUCCESS
        fi
        # tgz not exists - return SUCCESS
    # dir not exists
    else
        # tgz exists - extract_tgz
        if [ -f "${tgz_path}" ]; then
            extract_tgz "${tgz_path}" "${dir_path}" "${rm_nodejs_dir}"
        # tgz not exists - return FAIL
        else
            echo "ERROR: ${tgz_path} NOT EXISTS"
            error=1
        fi
    fi

    if [ -d "${dir_path}" ]; then
        rm -rf "$CATALINA_BASE/deploy/${dir_name}"
        ln -s "${dir_path}" "$CATALINA_BASE/deploy/${dir_name}"
    fi

    return $error
}

update_pandora() {
    local pandora_ok=""
    local last_log error_log
    for PANDORA_NAME in "${PANDORA_NAME_LIST[@]}"; do
        if [ -z "$pandora_ok" ]; then
            # capture log while search pandora
            last_log=$(update_target "${PANDORA_NAME}.tgz" "${PANDORA_NAME}.sar")
            if [ "$?" -eq 0 ]; then
                # discard previous error log
                error_log="${last_log}"
                test -n "${error_log}" && echo "${error_log}" && error_log=""
                pandora_ok="${APP_HOME}/target/${PANDORA_NAME}.sar"
                echo "USING PANDORA: ${pandora_ok}"
            else
                # concat previous error log
                test -n "${error_log}" && error_log="${error_log}"$'\n'
                error_log="${error_log}${last_log}"
            fi
        else
            # rename old pandora tgz to "*.bak"
            rm -rf "${APP_HOME}/target/${PANDORA_NAME}."{sar,tgz.timestamp}
            tgz_path="${APP_HOME}/target/${PANDORA_NAME}.tgz"
            if [ -e "${tgz_path}" ]; then
                mv -T "${tgz_path}" "${tgz_path}.bak"
            fi
            # symbolic old pandora dir to ${PANDORA_NAME}.sar
            ln -sf "$(basename ${pandora_ok})" "${APP_HOME}/target/${PANDORA_NAME}.sar"
        fi
    done
    # echo error_log at end
    test -n "${error_log}" && echo "${error_log}" && error_log=""
    test -n "${pandora_ok}" && test -d "${pandora_ok}"
}

start() {
    # prepare_catalina_out
    # delete old $CATALINA_OUT, keep last 20 logs
    ls "$CATALINA_OUT".* 2>/dev/null | tail -n +$((20 + 1)) | xargs --no-run-if-empty rm -f
    if [ -e "$CATALINA_OUT" ]; then
        mv "$CATALINA_OUT" "$CATALINA_OUT.$(date '+%Y%m%d%H%M%S')" || exit 1
    fi
    mkdir -p "$(dirname "${CATALINA_OUT}")" || exit 1
    touch "$CATALINA_OUT" || exit 1

    # show locale
    locale >> "${CATALINA_OUT}"
    fix_jvm_opts CATALINA_OPTS >> "${CATALINA_OUT}"

    # print start info to both ${CATALINA_OUT} and console.
    do_start | tee -a "${CATALINA_OUT}"
    if ! { test -r "${CATALINA_PID}" && kill -0 "$(cat "${CATALINA_PID}")"; }; then
        exit 1
    fi

    # create symbolic link jboss_stdout.log to tomcat_stdout.logs for compatibility purpose.
    if [ -L "${JBOSS_OUT}" ]; then
        echo "Found existing jboss_stdout.log symbolic link. ignore"
    else
        if [ -e "${JBOSS_OUT}" ]; then
            echo "Found existing jboss_stdout.log file. backup"
            mv "${JBOSS_OUT}" "${JBOSS_OUT}.$(date '+%Y%m%d%H%M%S')"
        fi
        echo "creating symbolic link jboss_stdout.log to tomcat_stdout.log"
        ln -s "${CATALINA_OUT}" "${JBOSS_OUT}"
    fi
}

do_start() {
    check_catalina_pid
    prepare_catalina_base
    mkdir -p "${APP_HOME}/target" || exit 1
    mkdir -p "${APP_HOME}/logs" || exit 1
    mkdir -p "${MIDDLEWARE_LOGS}" || exit 1
    mkdir -p "${MIDDLEWARE_SNAPSHOTS}" || exit 1
    # create symlink for middleware logs.
    ln -sfT "${MIDDLEWARE_LOGS}" "${APP_HOME}/logs/middleware"

    # update app
    update_target "${APP_NAME}.tgz" "${APP_NAME}.war" "true" || exit 1
    update_pandora || exit 1

    echo "start tomcat"
    if [ "$JPDA_ENABLE" -eq 1 ]; then
        local JPDA_ARG="jpda"
    else
        local JPDA_ARG=""
    fi
    beforeStartApp
    # async start
    "$CATALINA_HOME"/bin/catalina.sh $JPDA_ARG start >> "${CATALINA_OUT}"
}

online() {
    touch -m $STATUSROOT_HOME/status.taobao || exit 1
    online_hsf
    echo "app auto online..."
}

online_hsf() {
    # using ss -ln4 instead of ss -tln.
    # ss -tln will cause core dump if redhat version <= 5.4
    # see http://gitlab.alibaba-inc.com/middleware/apps-deploy/issues/20
    # update 2015.2.13:
    # see http://gitlab.alibaba-inc.com/middleware/apps-deploy/issues/36
    check_hsf=`(/usr/sbin/ss -ln4 sport = :12200; /usr/sbin/ss -ln6 sport = :12200) | grep -c ":12200"`
    check_pandora=`(/usr/sbin/ss -ln4 sport = :12201; /usr/sbin/ss -ln6 sport = :12201) | grep -c ":12201"`
    if [ $check_hsf -ne 0 -a $check_pandora -ne 0 ]; then
        echo "Online hsf...."
        ret_str=`curl --max-time ${HSF_ONLINE_TIMEOUT} -s "http://localhost:12201/hsf/online?k=hsf" 2>&1`
        if echo "$ret_str" | grep "server is resumed to register on cs(dr)" &>/dev/null; then
            echo "hsf online success."
        else
            echo "hsf online failed in ${HSF_ONLINE_TIMEOUT} seconds."
            echo "You can adjust the value of HSF_ONLINE_TIMEOUT in setenv.sh"
            echo "Note that hsf version < ******* does not support this feature. Please upgrade to ******* and later. \
                  If you still want to use the lower version, please set -Dhsf.publish.delayed=false in setenv.sh"
            exit 6 # hsf online failed
        fi
    else
        if [ $check_hsf -eq 0 ]; then
            echo "WARN: port 12200 cannot be detected."
        fi
        if [ $check_pandora -eq 0 ]; then
            echo "WARN: port 12201 cannot be detected."
        fi
        echo "hsf online failed. HSF is NOT online!"
        exit 6 # hsf online failed
    fi
}

offline() {
    rm -f $STATUSROOT_HOME/status.taobao || exit 1
    offline_hsf
    echo "wait app offline..."
    # 修改原有的 下线时长从15s增加到90s
    for e in $(seq 90); do
        echo -n " $e"
        sleep 1
    done
    echo
}

offline_hsf() {
    check_hsf=`(/usr/sbin/ss -ln4 sport = :12200; /usr/sbin/ss -ln6 sport = :12200) | grep -c ":12200"`
    check_pandora=`(/usr/sbin/ss -ln4 sport = :12201; /usr/sbin/ss -ln6 sport = :12201) | grep -c ":12201"`
    if [ $check_hsf -ne 0 -a $check_pandora -ne 0 ]; then
        echo "Offline hsf...."
        ret_str=`curl --max-time ${HSF_ONLINE_TIMEOUT} -s "http://localhost:12201/hsf/offline?k=hsf" 2>&1`
        if echo "$ret_str" | grep "server is unregistered on cs(dr)" &>/dev/null; then
            echo "hsf offline success."
        else
            echo "hsf offline failed."
            exit 9 # hsf offline failed
        fi
    else
        if [ $check_hsf -eq 0 ]; then
            echo "WARN: port 12200 cannot be detected."
        fi
        if [ $check_pandora -eq 0 ]; then
            echo "WARN: port 12201 cannot be detected."
        fi
        echo "WARN: hsf offline failed."
        # DO NOT exit here
    fi
}

stop() {
    offline
    if [ "${NGINX_SKIP}" -ne "1" ]; then
        echo "stop nginx"
        "$NGINXCTL" stop
    fi
    echo "stop tomcat"
    if [ -f "$CATALINA_PID" ]; then
        local PID=$(cat "$CATALINA_PID")
        if kill -0 "$PID" 2>/dev/null; then
            beforeStopApp
            "$CATALINA_HOME"/bin/catalina.sh stop -force
            mv /home/<USER>/logs/gc.log /home/<USER>/logs/gc.log.`date +%Y%m%d%H%M%S`
            afterStopApp
        fi
    fi
}

backup() {
    if [ -f "${APP_HOME}/target/${APP_NAME}.tgz" ]; then
        mkdir -p "${APP_HOME}/target/backup" || exit 1
        tgz_time=$(date --reference "${APP_HOME}/target/${APP_NAME}.tgz" +"%Y%m%d%H%M%S")
        cp -f "${APP_HOME}/target/${APP_NAME}.tgz" "${APP_HOME}/target/backup/${APP_NAME}.war.${tgz_time}.tgz"
    fi
}

start_http() {
    exptime=0
    local PID=$(cat "$CATALINA_PID")
    while true
    do
      ###CHECK_TOMCAT_STATUS
      ret=`egrep "(startup failed due to previous errors|Cannot start server)" $CATALINA_OUT`
      if [ ! -z "$ret" ] || ! kill -0 "$PID" 2>/dev/null; then
          echo -e "\nTomcat startup failed."
          exit 4 # tomcat start failed
      fi
      ret=`fgrep "Server startup in" $CATALINA_OUT`
      if [ -z "$ret" ]; then
          sleep 1
          ((exptime++))
          echo -n -e "\rWait Tomcat Start: $exptime..."
      else
        echo
        . "$APP_HOME/bin/preload.sh"
        if [ $status -eq 1 ]; then
            echo "preload.sh check success"
            HSF_ONLINE=$(echo ${CATALINA_OPTS} | grep -o hsf.publish.delayed=\\w* | awk -F'=' '{print tolower($2);}')
            if [ "$HSF_ONLINE" == "true" ]; then
                echo "-Dhsf.publish.delayed=true is configured, calling online_hsf explicitly..."
                online_hsf
            else
                echo "Could not found -Dhsf.publish.delayed or -Dhsf.publish.delayed=false, skip calling online_hsf."
            fi
            touch -m $STATUSROOT_HOME/status.taobao
            if [ ! -f $STATUSROOT_HOME/status.taobao ];then
                echo "can't create status file, please check directory and disk space"
            else
                echo "created status file success"
            fi
        else
            echo "preload.sh check failed"
            exit 5 # preload.sh check failed
        fi
        afterStartApp
        if [ "${NGINX_SKIP}" -ne "1" ]; then
            echo "start nginx"
            "$NGINXCTL" start
            if [ "$?" == "0" ]; then
                echo "HTTP Start SUCCESS."
            else
                echo "HTTP Start Failed."
                exit 7 # nginx start failed
            fi
        fi
        if test -n "${STATUS_PORT}"; then
            sleep ${STATUS_TAOBAO_WAIT_TIME}
            status_code=`/usr/bin/curl -L -o /dev/null --connect-timeout 5 -s -w %{http_code}  "http://127.0.0.1:${STATUS_PORT}/status.taobao"`
            if [ x$status_code != x200 ];then
                echo "check http://127.0.0.1:${STATUS_PORT}/status.taobao failed with status ${status_code} after wait ${STATUS_TAOBAO_WAIT_TIME} seconds."
                echo "You can adjust STATUS_TAOBAO_WAIT_TIME in setenv.sh"
                echo "See http://gitlab.alibaba-inc.com/middleware/apps-deploy/issues/31"
                exit 8 # status.taobao check failed
            fi
            echo "check http://127.0.0.1:${STATUS_PORT}/status.taobao success"
        fi
        echo "app online success"
        return
      fi
    done
}

case "$ACTION" in
    start)
        start
    ;;
    stop)
        stop
    ;;
    pubstart)
        stop
        start
        start_http
    ;;
    online)
        online
    ;;
    offline)
        offline
    ;;
    restart)
        stop
        start
        start_http
    ;;
    deploy)
        stop
        start
        start_http
        backup
    ;;
    *)
        usage
    ;;
esac