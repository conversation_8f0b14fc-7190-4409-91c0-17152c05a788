{"compileOnSave": true, "compilerOptions": {"target": "es2018", "module": "commonjs", "moduleResolution": "node", "allowJs": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "inlineSourceMap": true, "noImplicitThis": true, "noUnusedLocals": false, "stripInternal": true, "skipLibCheck": true, "pretty": true, "declaration": true, "forceConsistentCasingInFileNames": true, "typeRoots": ["./typings", "./node_modules/@types"], "outDir": "dist", "inlineSources": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "exclude": ["dist", "node_modules"], "include": ["src", "scripts", "src/controller/.ts"]}